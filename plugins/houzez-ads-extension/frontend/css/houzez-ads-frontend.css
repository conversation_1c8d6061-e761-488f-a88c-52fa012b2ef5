/**
 * Frontend styles for Houzez Ads Extension
 * Integrates with <PERSON><PERSON><PERSON> theme styling
 */

/* Ad Campaign Display */
.houzez-ad-campaign {
    margin: 20px 0;
    text-align: center;
    position: relative;
}

.houzez-ad-campaign img {
    max-width: 100%;
    height: auto;
    border-radius: 6px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.houzez-ad-campaign img:hover {
    opacity: 0.95;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

/* Zone-specific styling matching Houzez design */
.houzez-ad-homepage {
    margin: 30px 0;
    padding: 0;
}

.houzez-ad-sidebar {
    margin: 20px 0;
    padding: 20px;
    background: var(--houzez-bg-color, #f8f9fa);
    border-radius: 8px;
    border: 1px solid var(--houzez-border-color, #e9ecef);
}

.houzez-ad-search {
    margin: 15px 0;
    padding: 15px;
    background: #fff;
    border: 1px solid var(--houzez-border-color, #e9ecef);
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.houzez-ad-property-detail {
    margin: 25px 0;
    padding: 20px;
    background: var(--houzez-bg-color, #f8f9fa);
    border-radius: 8px;
    border: 1px solid var(--houzez-border-color, #e9ecef);
}

/* Campaign Dashboard - Houzez Integration */
.houzez-ads-dashboard {
    /* Uses Houzez dashboard styles */
}

.houzez-ads-campaigns-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 25px;
    margin-top: 25px;
}

.houzez-ads-campaign-card {
    background: #fff;
    border: 1px solid var(--houzez-border-color, #e9ecef);
    border-radius: 8px;
    padding: 25px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.houzez-ads-campaign-card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.houzez-ads-campaign-status {
    display: inline-block;
    padding: 6px 14px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.houzez-ads-status-pending {
    background: var(--houzez-warning-bg, #fff3cd);
    color: var(--houzez-warning-text, #856404);
}

.houzez-ads-status-approved {
    background: var(--houzez-success-bg, #d4edda);
    color: var(--houzez-success-text, #155724);
}

.houzez-ads-status-rejected {
    background: var(--houzez-danger-bg, #f8d7da);
    color: var(--houzez-danger-text, #721c24);
}

.houzez-ads-status-expired {
    background: var(--houzez-secondary-bg, #e2e3e5);
    color: var(--houzez-secondary-text, #383d41);
}

/* Upload Form - Houzez Integration */
.houzez-ads-upload-form {
    /* Uses Houzez dashboard content block styles */
}

.houzez-ads-properties-selection {
    max-height: 300px;
    overflow-y: auto;
    background: var(--houzez-bg-light, #f8f9fa);
    border-radius: 6px;
}

.houzez-ads-property-checkbox {
    margin-bottom: 12px;
}

.houzez-ads-property-checkbox label {
    font-weight: 500;
    margin-left: 10px;
    cursor: pointer;
    color: var(--houzez-text-color, #333);
}

.houzez-ads-pricing-display {
    background: var(--houzez-primary-light, #e8f4fd);
    border: 1px solid var(--houzez-primary, #3498db);
    border-radius: 6px;
    padding: 20px;
    margin: 25px 0;
    text-align: center;
}

.houzez-ads-price {
    font-size: 28px;
    font-weight: 700;
    color: var(--houzez-primary, #2980b9);
    font-family: var(--houzez-heading-font, inherit);
}

/* Image Preview */
.houzez-ads-image-preview-container img {
    max-width: 100%;
    height: auto;
    border-radius: 6px;
    border: 1px solid var(--houzez-border-color, #e9ecef);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Form Validation */
.houzez-ads-form-errors {
    background: var(--houzez-danger-bg, #f8d7da);
    color: var(--houzez-danger-text, #721c24);
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    border: 1px solid var(--houzez-danger, #dc3545);
}

.houzez-ads-form-errors ul {
    margin: 0;
    padding-left: 20px;
}

.houzez-ads-form-errors li {
    margin-bottom: 5px;
}

/* Analytics */
.houzez-ads-analytics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.houzez-ads-analytics-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
}

.houzez-ads-analytics-number {
    font-size: 28px;
    font-weight: bold;
    color: #3498db;
    display: block;
}

.houzez-ads-analytics-label {
    font-size: 12px;
    color: #7f8c8d;
    text-transform: uppercase;
    margin-top: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .houzez-ads-campaigns-grid {
        grid-template-columns: 1fr;
    }
    
    .houzez-ads-analytics {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .houzez-ads-dashboard,
    .houzez-ads-upload-form {
        padding: 20px;
    }
}

/* Promoted/Sponsored property styling */
.houzez-promoted-property-wrapper {
    margin: 20px 0;
    position: relative;
}

.houzez-promoted-property {
    position: relative;
    border: 2px solid #f39c12 !important;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.2) !important;
}

.houzez-promoted-property::before {
    content: "Promoted";
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: #fff;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 15;
    box-shadow: 0 2px 8px rgba(243, 156, 18, 0.3);
    animation: promotedPulse 2s infinite;
}

@keyframes promotedPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.houzez-promoted-property .item-wrap {
    background: linear-gradient(135deg, #fff 0%, #fef9e7 100%);
}

.houzez-promoted-property:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.3) !important;
}

/* Legacy sponsored property indicator for backward compatibility */
.property-item.sponsored {
    position: relative;
    border: 2px solid #f39c12;
}

.property-item.sponsored::before {
    content: "Sponsored";
    position: absolute;
    top: 10px;
    right: 10px;
    background: #f39c12;
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    z-index: 10;
}

/* Agency Profile Cards */
.houzez-ads-homepage-agencies,
.houzez-ads-sidebar-agencies {
    margin: 20px 0;
}

.houzez-agency-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.houzez-agency-card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.houzez-agency-card-homepage {
    display: flex;
    align-items: center;
    min-height: 120px;
}

.houzez-agency-card-sidebar {
    text-align: center;
    padding: 15px;
}

.agency-card-content {
    display: flex;
    align-items: center;
    width: 100%;
}

.houzez-agency-card-sidebar .agency-card-content {
    flex-direction: column;
    text-align: center;
}

.agency-avatar {
    flex-shrink: 0;
    margin-right: 20px;
}

.houzez-agency-card-sidebar .agency-avatar {
    margin-right: 0;
    margin-bottom: 15px;
}

.agency-avatar img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border: 3px solid #f8f9fa;
}

.houzez-agency-card-sidebar .agency-avatar img {
    width: 60px;
    height: 60px;
}

.agency-info {
    flex: 1;
}

.agency-name {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
}

.houzez-agency-card-sidebar .agency-name {
    font-size: 16px;
}

.agency-name a {
    color: var(--houzez-primary-color, #00aaff);
    text-decoration: none;
    transition: color 0.3s ease;
}

.agency-name a:hover {
    color: var(--houzez-primary-dark, #0088cc);
}

.agency-title {
    color: #6c757d;
    margin: 0 0 10px 0;
    font-size: 14px;
}

.agency-contact {
    margin: 10px 0;
    font-size: 13px;
}

.agency-contact span {
    display: block;
    margin-bottom: 5px;
    color: #6c757d;
}

.agency-contact i {
    margin-right: 5px;
    width: 12px;
}

.agency-contact a {
    color: inherit;
    text-decoration: none;
}

.agency-contact a:hover {
    color: var(--houzez-primary-color, #00aaff);
}

.agency-actions {
    margin-top: 15px;
}

.agency-actions .btn {
    margin-right: 10px;
    margin-bottom: 5px;
    font-size: 12px;
    padding: 5px 12px;
}

.houzez-agency-card-sidebar .agency-actions .btn {
    display: block;
    width: 100%;
    margin-right: 0;
    margin-bottom: 8px;
}

/* Business Partnership Ads */
.houzez-ads-business-partnership {
    margin: 20px 0;
    text-align: center;
}

.houzez-ads-business-partnership .houzez-ad-campaign {
    display: inline-block;
    max-width: 100%;
}
