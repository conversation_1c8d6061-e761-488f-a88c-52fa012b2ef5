<?php
/**
 * Campaign upload form template using Houzez property form UI
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/frontend/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

$current_user_id = get_current_user_id();
$user_type = houzez_ads_get_user_type( $current_user_id );

// Get user-specific data
$user_properties = houzez_ads_get_user_properties( $current_user_id );

// Define available durations for all user types
$durations = array(
	'7' => __( '7 days - 10 credits', 'houzez-ads-extension' ),
	'14' => __( '14 days - 18 credits', 'houzez-ads-extension' ),
	'30' => __( '30 days - 30 credits', 'houzez-ads-extension' )
);
?>

<div class="dashboard-content-block-wrap">
	<div class="dashboard-content-block">
		<div class="dashboard-content-block-title">
			<?php
			// User-type-specific titles
			switch ( $user_type ) {
				case 'agency':
					_e( 'Create New Ad Campaign', 'houzez-ads-extension' );
					break;
				case 'business':
					_e( 'Create Business Partnership Ad', 'houzez-ads-extension' );
					break;
				default: // agent and regular users
					_e( 'Promote Your Properties', 'houzez-ads-extension' );
					break;
			}
			?>
		</div>

		<form autocomplete="off" id="houzez-ads-upload-form" method="post" action="#" enctype="multipart/form-data" class="add-frontend-property" novalidate>
			<?php wp_nonce_field( 'houzez_ads_upload_form', 'houzez_ads_nonce' ); ?>
			<input type="hidden" id="user_type" value="<?php echo esc_attr( $user_type ); ?>" />

			<div class="validate-errors alert alert-danger alert-dismissible fade show houzez-hidden" role="alert">
				<strong><?php _e( 'Error!', 'houzez-ads-extension' ); ?></strong> <?php _e( 'Please fill in all required fields.', 'houzez-ads-extension' ); ?>
				<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
			</div>

			<?php
			// Render user-type-specific form sections
			switch ( $user_type ) {
				case 'agency':
					include 'form-sections/agency-form.php';
					break;
				case 'business':
					include 'form-sections/business-form.php';
					break;
				default: // agent and regular users
					include 'form-sections/agent-form.php';
					break;
			}
			?>



			<!-- Submit Section -->
			<div class="d-flex justify-content-between p-2 add-new-listing-bottom-nav-wrap">
				<a href="<?php echo esc_url( houzez_ads_get_dashboard_url() ); ?>" class="btn-cancel btn btn-primary-outlined">
					<?php _e( 'Cancel', 'houzez-ads-extension' ); ?>
				</a>

				<button type="submit" class="btn houzez-submit-js btn-primary">
					<?php get_template_part('template-parts/loader'); ?>
					<?php _e( 'Create Campaign', 'houzez-ads-extension' ); ?>
				</button>
			</div>
		</form>
	</div>
</div>

<script>
jQuery(document).ready(function($) {
	var userType = $('#user_type').val();

	// Handle form submission
	$('#houzez-ads-upload-form').on('submit', function(e) {
		e.preventDefault();

		var $form = $(this);
		var $submitBtn = $form.find('button[type="submit"]');
		var $errorDiv = $form.find('.validate-errors');

		// Show loading state
		$submitBtn.prop('disabled', true);
		$submitBtn.find('.houzez-loader-js').show();
		$errorDiv.addClass('houzez-hidden');

		// Submit via AJAX
		$.ajax({
			url: houzez_ads_ajax.ajax_url,
			type: 'POST',
			data: $form.serialize() + '&action=houzez_ads_create_campaign',
			success: function(response) {
				if (response.success) {
					// Show success message
					$('<div class="alert alert-success alert-dismissible fade show" role="alert">' +
						'<strong><?php _e( "Success!", "houzez-ads-extension" ); ?></strong> ' + response.data.message +
						'<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
					'</div>').insertBefore($form);

					// Redirect after 2 seconds
					setTimeout(function() {
						if (response.data.redirect_url) {
							window.location.href = response.data.redirect_url;
						} else {
							location.reload();
						}
					}, 2000);
				} else {
					// Show error message
					$errorDiv.find('strong').next().text(response.data.message || '<?php _e( "An error occurred", "houzez-ads-extension" ); ?>');
					$errorDiv.removeClass('houzez-hidden');

					// Reset button
					$submitBtn.prop('disabled', false);
					$submitBtn.find('.houzez-loader-js').hide();
				}
			},
			error: function() {
				// Show generic error
				$errorDiv.find('strong').next().text('<?php _e( "An error occurred while creating the campaign", "houzez-ads-extension" ); ?>');
				$errorDiv.removeClass('houzez-hidden');

				// Reset button
				$submitBtn.prop('disabled', false);
				$submitBtn.find('.houzez-loader-js').hide();
			}
		});
	});

	// Dynamic pricing update
	$('#ad_zone, #duration').on('change', function() {
		var zone = $('#ad_zone').val();
		var duration = $('#duration').val() || '30';

		if (zone) {
			$.ajax({
				url: houzez_ads_ajax.ajax_url,
				type: 'POST',
				data: {
					action: 'houzez_ads_get_pricing',
					zone: zone,
					duration: duration,
					user_type: userType,
					nonce: houzez_ads_ajax.nonce
				},
				success: function(response) {
					if (response.success) {
						$('.houzez-ads-price').text(response.data.formatted_price);
						$('.houzez-ads-pricing-display').show();
					}
				}
			});
		}
	});

	// Agency-specific: Handle ad type changes for zone updates
	if (userType === 'agency') {
		var availableZones = {
			'profile': {
				'homepage': '<?php _e( "Homepage Banner", "houzez-ads-extension" ); ?>',
				'sidebar': '<?php _e( "Sidebar Ads", "houzez-ads-extension" ); ?>'
			},
			'property': {
				'homepage': '<?php _e( "Homepage Banner", "houzez-ads-extension" ); ?>',
				'sidebar': '<?php _e( "Sidebar Ads", "houzez-ads-extension" ); ?>',
				'search': '<?php _e( "Search Results", "houzez-ads-extension" ); ?>',
				'property_detail': '<?php _e( "Property Detail Page", "houzez-ads-extension" ); ?>'
			}
		};

		$('#ad_type').on('change', function() {
			var adType = $(this).val();
			var $zoneSelect = $('#ad_zone');
			var zones = availableZones[adType] || {};

			// Update zone descriptions
			var descriptions = {
				'profile': '<?php _e( "Promote your agency profile on homepage and sidebar", "houzez-ads-extension" ); ?>',
				'property': '<?php _e( "Promote specific properties across all zones", "houzez-ads-extension" ); ?>'
			};
			$('#zone-description').text(descriptions[adType] || '');

			// Clear and populate zones
			$zoneSelect.empty().append('<option value=""><?php _e( "Select Ad Zone", "houzez-ads-extension" ); ?></option>');
			$.each(zones, function(value, label) {
				$zoneSelect.append('<option value="' + value + '">' + label + '</option>');
			});

			// Refresh selectpicker
			if ($zoneSelect.hasClass('selectpicker')) {
				$zoneSelect.selectpicker('refresh');
			}

			// Show/hide property selection
			if (adType === 'property') {
				$('.houzez-ads-property-selection-wrapper').show();
			} else {
				$('.houzez-ads-property-selection-wrapper').hide();
			}
		});

		// Initialize
		$('#ad_type').trigger('change');
	}

	// Property selection counter
	$('.property-checkbox').on('change', function() {
		var selectedCount = $('.property-checkbox:checked').length;
		$('.selected-count').text(selectedCount);
	});

	// Business form: Banner preview
	if (userType === 'business') {
		$('#banner_image').on('input', function() {
			var imageUrl = $(this).val();
			if (imageUrl && (imageUrl.match(/\.(jpeg|jpg|gif|png)$/i))) {
				$('#preview-image').attr('src', imageUrl);
				$('#banner-preview').show();
			} else {
				$('#banner-preview').hide();
			}
		});
	}

	// Initialize pricing display
	$('#ad_zone').trigger('change');
});
</script>
