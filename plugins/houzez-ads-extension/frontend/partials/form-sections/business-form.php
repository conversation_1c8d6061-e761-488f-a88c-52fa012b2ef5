<?php
/**
 * Business user form section
 * Shows only business partnership fields with banner upload
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/frontend/partials/form-sections
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

$business_zones = array(
	'homepage' => __( 'Homepage Banner', 'houzez-ads-extension' ),
	'sidebar' => __( 'Sidebar Ads', 'houzez-ads-extension' ),
	'search' => __( 'Search Results', 'houzez-ads-extension' ),
	'property_detail' => __( 'Property Detail Page', 'houzez-ads-extension' )
);
?>

<!-- Hidden fields for business users -->
<input type="hidden" name="ad_type" value="partner" />

<!-- Campaign Details Section -->
<div class="form-step">
	<div class="dashboard-content-block-wrap">
		<div class="dashboard-content-block">
			<div class="dashboard-content-block-title">
				<?php _e( 'Business Partnership Details', 'houzez-ads-extension' ); ?>
			</div>

			<div class="row">
				<div class="col-md-6">
					<div class="form-group mb-3">
						<label class="form-label" for="ad_zone">
							<?php _e( 'Ad Zone', 'houzez-ads-extension' ); ?> <span class="required-field">*</span>
						</label>
						<select name="ad_zone" id="ad_zone" class="selectpicker form-control" title="<?php _e( 'Select Ad Zone', 'houzez-ads-extension' ); ?>" required>
							<?php foreach ( $business_zones as $key => $label ) : ?>
								<option value="<?php echo esc_attr( $key ); ?>"><?php echo esc_html( $label ); ?></option>
							<?php endforeach; ?>
						</select>
						<small class="form-text text-muted">
							<?php _e( 'Choose where your business partnership ad will be displayed.', 'houzez-ads-extension' ); ?>
						</small>
					</div>
				</div>

				<div class="col-md-6">
					<div class="form-group mb-3">
						<label class="form-label" for="duration">
							<?php _e( 'Campaign Duration', 'houzez-ads-extension' ); ?> <span class="required-field">*</span>
						</label>
						<select name="duration" id="duration" class="selectpicker form-control" title="<?php _e( 'Select Duration', 'houzez-ads-extension' ); ?>" required>
							<?php foreach ( $durations as $value => $label ) : ?>
								<option value="<?php echo esc_attr( $value ); ?>"><?php echo esc_html( $label ); ?></option>
							<?php endforeach; ?>
						</select>
					</div>
				</div>
			</div>

			<div class="row">
				<div class="col-md-6">
					<div class="form-group mb-3 houzez-ads-pricing-display" style="display: none;">
						<label class="form-label">
							<?php _e( 'Campaign Price', 'houzez-ads-extension' ); ?>
						</label>
						<div class="alert alert-info">
							<strong class="houzez-ads-price">0 credits</strong>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- Banner Media Section -->
<div class="form-step">
	<div class="dashboard-content-block-wrap">
		<div class="dashboard-content-block">
			<div class="dashboard-content-block-title">
				<?php _e( 'Banner Media & Links', 'houzez-ads-extension' ); ?>
			</div>

			<div class="row">
				<div class="col-md-6">
					<div class="form-group mb-3">
						<label class="form-label" for="banner_image">
							<?php _e( 'Banner Image URL', 'houzez-ads-extension' ); ?> <span class="required-field">*</span>
						</label>
						<input type="url" name="banner_image" id="banner_image" class="form-control" placeholder="<?php _e( 'https://example.com/banner.jpg', 'houzez-ads-extension' ); ?>" required />
						<small class="form-text text-muted">
							<?php _e( 'Recommended size: 728x90px for homepage, 300x250px for sidebar', 'houzez-ads-extension' ); ?>
						</small>
					</div>
				</div>

				<div class="col-md-6">
					<div class="form-group mb-3">
						<label class="form-label" for="banner_link">
							<?php _e( 'Banner Link URL', 'houzez-ads-extension' ); ?> <span class="required-field">*</span>
						</label>
						<input type="url" name="banner_link" id="banner_link" class="form-control" placeholder="<?php _e( 'https://your-website.com', 'houzez-ads-extension' ); ?>" required />
						<small class="form-text text-muted">
							<?php _e( 'Where users will be redirected when they click your banner', 'houzez-ads-extension' ); ?>
						</small>
					</div>
				</div>
			</div>

			<div class="row">
				<div class="col-md-6">
					<div class="form-group mb-3">
						<label class="form-label" for="banner_alt">
							<?php _e( 'Banner Alt Text', 'houzez-ads-extension' ); ?>
						</label>
						<input type="text" name="banner_alt" id="banner_alt" class="form-control" placeholder="<?php _e( 'Describe your banner image', 'houzez-ads-extension' ); ?>" />
						<small class="form-text text-muted">
							<?php _e( 'Alternative text for accessibility and SEO', 'houzez-ads-extension' ); ?>
						</small>
					</div>
				</div>

				<div class="col-md-6">
					<div class="form-group mb-3">
						<label class="form-label">
							<?php _e( 'Banner Preview', 'houzez-ads-extension' ); ?>
						</label>
						<div class="banner-preview-container">
							<div id="banner-preview" class="banner-preview" style="display: none;">
								<img id="preview-image" src="" alt="" style="max-width: 100%; height: auto; border: 1px solid #ddd;" />
							</div>
							<small class="form-text text-muted">
								<?php _e( 'Preview will appear when you enter a valid image URL', 'houzez-ads-extension' ); ?>
							</small>
						</div>
					</div>
				</div>
			</div>

			<div class="row">
				<div class="col-12">
					<div class="alert alert-info">
						<h6><?php _e( 'Business Partnership Guidelines', 'houzez-ads-extension' ); ?></h6>
						<ul class="mb-0">
							<li><?php _e( 'Banner images should be high quality and professional', 'houzez-ads-extension' ); ?></li>
							<li><?php _e( 'Content must be relevant to real estate industry', 'houzez-ads-extension' ); ?></li>
							<li><?php _e( 'No misleading or inappropriate content allowed', 'houzez-ads-extension' ); ?></li>
							<li><?php _e( 'All ads are subject to admin approval', 'houzez-ads-extension' ); ?></li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
