<?php
/**
 * Agency user form section
 * Shows ad type selection with conditional zones and duration
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/frontend/partials/form-sections
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

$ad_types = array(
	'profile' => __( 'Agency/Profile Promotion', 'houzez-ads-extension' ),
	'property' => __( 'Property Promotion', 'houzez-ads-extension' )
);
?>

<!-- Campaign Details Section -->
<div class="form-step">
	<div class="dashboard-content-block-wrap">
		<div class="dashboard-content-block">
			<div class="dashboard-content-block-title">
				<?php _e( 'Campaign Details', 'houzez-ads-extension' ); ?>
			</div>

			<div class="row">
				<div class="col-md-6">
					<div class="form-group mb-3">
						<label class="form-label" for="ad_type">
							<?php _e( 'Ad Type', 'houzez-ads-extension' ); ?> <span class="required-field">*</span>
						</label>
						<select name="ad_type" id="ad_type" class="selectpicker form-control" title="<?php _e( 'Select Ad Type', 'houzez-ads-extension' ); ?>" required>
							<?php foreach ( $ad_types as $key => $label ) : ?>
								<option value="<?php echo esc_attr( $key ); ?>"><?php echo esc_html( $label ); ?></option>
							<?php endforeach; ?>
						</select>
						<small class="form-text text-muted">
							<strong><?php _e( 'Profile Promotion:', 'houzez-ads-extension' ); ?></strong> <?php _e( 'Promote your agency profile', 'houzez-ads-extension' ); ?><br>
							<strong><?php _e( 'Property Promotion:', 'houzez-ads-extension' ); ?></strong> <?php _e( 'Promote specific properties', 'houzez-ads-extension' ); ?>
						</small>
					</div>
				</div>

				<div class="col-md-6">
					<div class="form-group mb-3">
						<label class="form-label" for="ad_zone">
							<?php _e( 'Ad Zone', 'houzez-ads-extension' ); ?> <span class="required-field">*</span>
						</label>
						<select name="ad_zone" id="ad_zone" class="selectpicker form-control" title="<?php _e( 'Select Ad Zone', 'houzez-ads-extension' ); ?>" required>
							<!-- Options will be populated dynamically based on ad type -->
						</select>
						<small class="form-text text-muted" id="zone-description">
							<?php _e( 'Select ad type first to see available zones', 'houzez-ads-extension' ); ?>
						</small>
					</div>
				</div>
			</div>

			<div class="row">
				<div class="col-md-6">
					<div class="form-group mb-3">
						<label class="form-label" for="duration">
							<?php _e( 'Campaign Duration', 'houzez-ads-extension' ); ?> <span class="required-field">*</span>
						</label>
						<select name="duration" id="duration" class="selectpicker form-control" title="<?php _e( 'Select Duration', 'houzez-ads-extension' ); ?>" required>
							<?php foreach ( $durations as $value => $label ) : ?>
								<option value="<?php echo esc_attr( $value ); ?>"><?php echo esc_html( $label ); ?></option>
							<?php endforeach; ?>
						</select>
					</div>
				</div>

				<div class="col-md-6">
					<div class="form-group mb-3 houzez-ads-pricing-display" style="display: none;">
						<label class="form-label">
							<?php _e( 'Campaign Price', 'houzez-ads-extension' ); ?>
						</label>
						<div class="alert alert-info">
							<strong class="houzez-ads-price">0 credits</strong>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- Property Selection Section (shown only for property ads) -->
<div class="form-step houzez-ads-property-selection-wrapper" style="display: none;">
	<div class="dashboard-content-block-wrap">
		<div class="dashboard-content-block">
			<div class="dashboard-content-block-title">
				<?php _e( 'Select Properties to Promote', 'houzez-ads-extension' ); ?>
			</div>

			<?php if ( ! empty( $user_properties ) ) : ?>
				<div class="houzez-ads-property-selection">
					<div class="row">
						<?php foreach ( $user_properties as $property ) : ?>
							<div class="col-md-6 col-lg-4 mb-3">
								<div class="property-selection-item">
									<label class="property-checkbox-label">
										<input type="checkbox" name="selected_properties[]" value="<?php echo esc_attr( $property->ID ); ?>" class="property-checkbox" />
										<div class="property-card">
											<?php if ( has_post_thumbnail( $property->ID ) ) : ?>
												<div class="property-image">
													<?php echo get_the_post_thumbnail( $property->ID, 'thumbnail' ); ?>
												</div>
											<?php endif; ?>
											<div class="property-details">
												<h6 class="property-title"><?php echo esc_html( $property->post_title ); ?></h6>
												<p class="property-status">
													<span class="badge badge-<?php echo $property->post_status === 'publish' ? 'success' : 'warning'; ?>">
														<?php echo esc_html( ucfirst( $property->post_status ) ); ?>
													</span>
												</p>
											</div>
										</div>
									</label>
								</div>
							</div>
						<?php endforeach; ?>
					</div>
					<div class="selected-properties-info mt-3">
						<div class="alert alert-info">
							<strong><?php _e( 'Selected Properties:', 'houzez-ads-extension' ); ?></strong>
							<span class="selected-count">0</span>
							<span class="selected-text"><?php _e( 'properties selected', 'houzez-ads-extension' ); ?></span>
						</div>
					</div>
				</div>
			<?php else : ?>
				<div class="alert alert-warning">
					<h6><?php _e( 'No Properties Found', 'houzez-ads-extension' ); ?></h6>
					<p><?php _e( 'You need to have published properties to create property promotion campaigns.', 'houzez-ads-extension' ); ?></p>
					<a href="<?php echo esc_url( houzez_get_template_link( 'template/submit_property.php' ) ); ?>" class="btn btn-primary">
						<?php _e( 'Add New Property', 'houzez-ads-extension' ); ?>
					</a>
				</div>
			<?php endif; ?>
		</div>
	</div>
</div>
