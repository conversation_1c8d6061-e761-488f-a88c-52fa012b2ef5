<?php
/**
 * Campaign dashboard template using Houzez dashboard UI
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/frontend/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

$current_user_id = get_current_user_id();

// Check if we should show the create form
$show_create_form = isset( $_GET['create_campaign'] ) && $_GET['create_campaign'] == '1';

if ( $show_create_form ) {
	// Show the upload form instead of dashboard
	echo do_shortcode( '[houzez_ads_upload]' );
	return;
}

$campaigns = get_posts( array(
	'post_type' => 'banner_campaign',
	'author' => $current_user_id,
	'posts_per_page' => -1,
	'post_status' => array( 'publish', 'draft', 'pending' ),
	'orderby' => 'date',
	'order' => 'DESC'
) );

$statuses = houzez_ads_get_campaign_statuses();
?>

<div class="dashboard-content-block-wrap">
	<div class="dashboard-content-block">
		<div class="d-flex align-items-center justify-content-between">
			<div class="dashboard-content-block-title">
				<?php _e( 'My Ad Campaigns', 'houzez-ads-extension' ); ?>
			</div>
			<div>
				<a href="<?php echo esc_url( add_query_arg( 'create_campaign', '1' ) ); ?>" class="btn btn-primary">
					<i class="houzez-icon icon-add-circle me-2"></i>
					<?php _e( 'Create New Campaign', 'houzez-ads-extension' ); ?>
				</a>
			</div>
		</div>
	</div>

	<?php if ( ! empty( $campaigns ) ) : ?>
		<div class="houzez-data-content">
			<div class="houzez-table-filters">
				<div class="row">
					<div class="col-md-4">
						<div class="dashboard-search-filter">
							<span><i class="houzez-icon icon-search-1"></i></span>
							<input type="text" id="campaign-search" class="form-control dashboard-search" placeholder="<?php _e( 'Search campaigns...', 'houzez-ads-extension' ); ?>">
						</div>
					</div>
					<div class="col-md-4">
						<select id="campaign-status-filter" class="selectpicker form-control" title="<?php _e( 'Filter by Status', 'houzez-ads-extension' ); ?>">
							<option value=""><?php _e( 'All Statuses', 'houzez-ads-extension' ); ?></option>
							<?php foreach ( $statuses as $key => $label ) : ?>
								<option value="<?php echo esc_attr( $key ); ?>"><?php echo esc_html( $label ); ?></option>
							<?php endforeach; ?>
						</select>
					</div>
					<div class="col-md-4">
						<select id="campaign-zone-filter" class="selectpicker form-control" title="<?php _e( 'Filter by Zone', 'houzez-ads-extension' ); ?>">
							<option value=""><?php _e( 'All Zones', 'houzez-ads-extension' ); ?></option>
							<?php 
							$zones = houzez_ads_get_available_zones();
							foreach ( $zones as $key => $label ) : ?>
								<option value="<?php echo esc_attr( $key ); ?>"><?php echo esc_html( $label ); ?></option>
							<?php endforeach; ?>
						</select>
					</div>
				</div>
			</div>

			<div class="table-responsive">
				<table class="table table-hover">
					<thead>
						<tr>
							<th><?php _e( 'Campaign', 'houzez-ads-extension' ); ?></th>
							<th><?php _e( 'Type', 'houzez-ads-extension' ); ?></th>
							<th><?php _e( 'Zone', 'houzez-ads-extension' ); ?></th>
							<th><?php _e( 'Duration', 'houzez-ads-extension' ); ?></th>
							<th><?php _e( 'Status', 'houzez-ads-extension' ); ?></th>
							<th><?php _e( 'Analytics', 'houzez-ads-extension' ); ?></th>
							<th><?php _e( 'Actions', 'houzez-ads-extension' ); ?></th>
						</tr>
					</thead>
					<tbody>
						<?php foreach ( $campaigns as $campaign_post ) : 
							$campaign = new Houzez_Banner_Campaign( $campaign_post );
							$analytics = $campaign->get_analytics();
							$ad_types = houzez_ads_get_available_ad_types();
							$ad_zones = houzez_ads_get_available_zones();
						?>
							<tr data-campaign-id="<?php echo esc_attr( $campaign->id ); ?>" 
								data-status="<?php echo esc_attr( $campaign->campaign_status ); ?>"
								data-zone="<?php echo esc_attr( $campaign->ad_zone ); ?>">
								<td>
									<div class="d-flex align-items-center">
										<?php if ( $campaign->banner_image ) : ?>
											<img src="<?php echo esc_url( $campaign->banner_image ); ?>" 
												 alt="<?php echo esc_attr( $campaign->banner_alt ); ?>"
												 class="me-3" 
												 style="width: 60px; height: 40px; object-fit: cover; border-radius: 4px;" />
										<?php endif; ?>
										<div>
											<strong><?php echo esc_html( $campaign->title ); ?></strong>
											<br>
											<small class="text-muted">
												<?php printf( __( 'Created: %s', 'houzez-ads-extension' ), date_i18n( get_option( 'date_format' ), strtotime( $campaign_post->post_date ) ) ); ?>
											</small>
										</div>
									</div>
								</td>
								<td>
									<span class="badge bg-secondary">
										<?php echo esc_html( $ad_types[ $campaign->ad_type ] ?? $campaign->ad_type ); ?>
									</span>
								</td>
								<td>
									<?php echo esc_html( $ad_zones[ $campaign->ad_zone ] ?? $campaign->ad_zone ); ?>
								</td>
								<td>
									<?php printf( _n( '%d day', '%d days', $campaign->duration, 'houzez-ads-extension' ), $campaign->duration ); ?>
									<?php if ( $campaign->end_date ) : ?>
										<br>
										<small class="text-muted">
											<?php 
											$days_remaining = ceil( ( strtotime( $campaign->end_date ) - current_time( 'timestamp' ) ) / ( 24 * 60 * 60 ) );
											if ( $days_remaining > 0 ) {
												printf( __( '%d days left', 'houzez-ads-extension' ), $days_remaining );
											} else {
												_e( 'Expired', 'houzez-ads-extension' );
											}
											?>
										</small>
									<?php endif; ?>
								</td>
								<td>
									<?php 
									$status_class = 'bg-secondary';
									switch ( $campaign->campaign_status ) {
										case 'approved':
											$status_class = 'bg-success';
											break;
										case 'pending':
											$status_class = 'bg-warning';
											break;
										case 'rejected':
											$status_class = 'bg-danger';
											break;
										case 'expired':
											$status_class = 'bg-dark';
											break;
									}
									?>
									<span class="badge <?php echo esc_attr( $status_class ); ?>">
										<?php echo esc_html( $statuses[ $campaign->campaign_status ] ?? $campaign->campaign_status ); ?>
									</span>
								</td>
								<td>
									<?php if ( $campaign->campaign_status === 'approved' ) : ?>
										<div class="small">
											<div><?php printf( __( 'Views: %s', 'houzez-ads-extension' ), number_format( $analytics['impressions'] ) ); ?></div>
											<div><?php printf( __( 'Clicks: %s', 'houzez-ads-extension' ), number_format( $analytics['clicks'] ) ); ?></div>
											<div><?php printf( __( 'CTR: %s%%', 'houzez-ads-extension' ), $analytics['ctr'] ); ?></div>
										</div>
									<?php else : ?>
										<span class="text-muted"><?php _e( 'N/A', 'houzez-ads-extension' ); ?></span>
									<?php endif; ?>
								</td>
								<td>
									<div class="dropdown">
										<button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
											<?php _e( 'Actions', 'houzez-ads-extension' ); ?>
										</button>
										<ul class="dropdown-menu">
											<li>
												<a class="dropdown-item" href="<?php echo esc_url( admin_url( 'post.php?post=' . $campaign->id . '&action=edit' ) ); ?>">
													<i class="houzez-icon icon-edit me-2"></i>
													<?php _e( 'Edit', 'houzez-ads-extension' ); ?>
												</a>
											</li>
											<?php if ( $campaign->banner_link ) : ?>
												<li>
													<a class="dropdown-item" href="<?php echo esc_url( $campaign->banner_link ); ?>" target="_blank">
														<i class="houzez-icon icon-link me-2"></i>
														<?php _e( 'View Target', 'houzez-ads-extension' ); ?>
													</a>
												</li>
											<?php endif; ?>
											<?php if ( $campaign->order_id && houzez_ads_is_woocommerce_active() ) : ?>
												<?php
												$order_edit_url = houzez_ads_get_order_edit_url( $campaign->order_id );
												if ( $order_edit_url ) :
												?>
													<li>
														<a class="dropdown-item" href="<?php echo esc_url( $order_edit_url ); ?>">
															<i class="houzez-icon icon-receipt me-2"></i>
															<?php _e( 'View Order', 'houzez-ads-extension' ); ?>
														</a>
													</li>
												<?php endif; ?>
											<?php endif; ?>
											<li><hr class="dropdown-divider"></li>
											<li>
												<a class="dropdown-item text-danger houzez-ads-delete-campaign"
												   href="#"
												   data-campaign-id="<?php echo esc_attr( $campaign_post->ID ); ?>"
												   data-campaign-title="<?php echo esc_attr( $campaign->title ); ?>">
													<i class="houzez-icon icon-delete me-2"></i>
													<?php _e( 'Delete', 'houzez-ads-extension' ); ?>
												</a>
											</li>
										</ul>
									</div>
								</td>
							</tr>
						<?php endforeach; ?>
					</tbody>
				</table>
			</div>
		</div>
	<?php else : ?>
		<div class="dashboard-content-block">
			<div class="text-center py-5">
				<i class="houzez-icon icon-megaphone" style="font-size: 64px; color: #ddd;"></i>
				<h4 class="mt-3"><?php _e( 'No campaigns yet', 'houzez-ads-extension' ); ?></h4>
				<p class="text-muted"><?php _e( 'Create your first ad campaign to start promoting your properties and services.', 'houzez-ads-extension' ); ?></p>
				<a href="<?php echo esc_url( add_query_arg( 'create_campaign', '1' ) ); ?>" class="btn btn-primary">
					<i class="houzez-icon icon-add-circle me-2"></i>
					<?php _e( 'Create Your First Campaign', 'houzez-ads-extension' ); ?>
				</a>
			</div>
		</div>
	<?php endif; ?>
</div>

<script>
jQuery(document).ready(function($) {
	// Search functionality
	$('#campaign-search').on('keyup', function() {
		var value = $(this).val().toLowerCase();
		$('tbody tr').filter(function() {
			$(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
		});
	});

	// Status filter
	$('#campaign-status-filter').on('change', function() {
		var status = $(this).val();
		if (status === '') {
			$('tbody tr').show();
		} else {
			$('tbody tr').hide();
			$('tbody tr[data-status="' + status + '"]').show();
		}
	});

	// Zone filter
	$('#campaign-zone-filter').on('change', function() {
		var zone = $(this).val();
		if (zone === '') {
			$('tbody tr').show();
		} else {
			$('tbody tr').hide();
			$('tbody tr[data-zone="' + zone + '"]').show();
		}
	});

	// Delete campaign functionality
	$('.houzez-ads-delete-campaign').on('click', function(e) {
		e.preventDefault();

		var campaignId = $(this).data('campaign-id');
		var campaignTitle = $(this).data('campaign-title');
		var $row = $(this).closest('tr');

		if (confirm('<?php _e( "Are you sure you want to delete the campaign", "houzez-ads-extension" ); ?> "' + campaignTitle + '"?')) {
			$.ajax({
				url: houzez_ads_ajax.ajax_url,
				type: 'POST',
				data: {
					action: 'houzez_ads_delete_campaign',
					campaign_id: campaignId,
					nonce: '<?php echo wp_create_nonce( "houzez_ads_delete_campaign" ); ?>'
				},
				beforeSend: function() {
					$row.css('opacity', '0.5');
				},
				success: function(response) {
					if (response.success) {
						$row.fadeOut(300, function() {
							$(this).remove();
							// Check if no campaigns left
							if ($('tbody tr').length === 0) {
								location.reload();
							}
						});
					} else {
						alert(response.data.message || '<?php _e( "Failed to delete campaign", "houzez-ads-extension" ); ?>');
						$row.css('opacity', '1');
					}
				},
				error: function() {
					alert('<?php _e( "An error occurred while deleting the campaign", "houzez-ads-extension" ); ?>');
					$row.css('opacity', '1');
				}
			});
		}
	});
});
</script>
