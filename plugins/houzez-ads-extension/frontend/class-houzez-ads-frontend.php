<?php
/**
 * The public-facing functionality of the plugin
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/frontend
 */

/**
 * The public-facing functionality of the plugin.
 *
 * Defines the plugin name, version, and hooks for public-facing functionality.
 */
class Houzez_Ads_Frontend {

	/**
	 * The ID of this plugin.
	 *
	 * @var string $plugin_name The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @var string $version The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @param string $plugin_name The name of the plugin.
	 * @param string $version     The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {
		$this->plugin_name = $plugin_name;
		$this->version = $version;
	}

	/**
	 * Register the stylesheets for the public-facing side of the site.
	 */
	public function enqueue_styles() {
		wp_enqueue_style( 
			$this->plugin_name, 
			HOUZEZ_ADS_EXTENSION_URL . 'frontend/css/houzez-ads-frontend.css', 
			array(), 
			$this->version, 
			'all' 
		);
	}

	/**
	 * Register the JavaScript for the public-facing side of the site.
	 */
	public function enqueue_scripts() {
		wp_enqueue_script( 
			$this->plugin_name, 
			HOUZEZ_ADS_EXTENSION_URL . 'frontend/js/houzez-ads-frontend.js', 
			array( 'jquery' ), 
			$this->version, 
			false 
		);

		// Localize script for AJAX tracking
		wp_localize_script( $this->plugin_name, 'houzez_ads_ajax', array(
			'ajax_url' => admin_url( 'admin-ajax.php' ),
			'nonce' => wp_create_nonce( 'houzez_ads_tracking_nonce' )
		));
	}

	/**
	 * Register shortcodes and frontend hooks.
	 */
	public function register_shortcodes() {
		add_shortcode( 'houzez_ads_zone', array( $this, 'display_ad_zone' ) );
		add_shortcode( 'houzez_ads_dashboard', array( $this, 'display_campaign_dashboard' ) );
		add_shortcode( 'houzez_ads_upload', array( $this, 'display_upload_form' ) );
		add_shortcode( 'houzez_ads_homepage_agencies', array( $this, 'display_homepage_agencies' ) );
		add_shortcode( 'houzez_ads_sidebar_agencies', array( $this, 'display_sidebar_agencies' ) );
		add_shortcode( 'houzez_ads_business_partnership', array( $this, 'display_business_partnership_ad' ) );

		// Register AJAX handlers
		add_action( 'wp_ajax_houzez_ads_track_click', array( $this, 'handle_click_tracking' ) );
		add_action( 'wp_ajax_nopriv_houzez_ads_track_click', array( $this, 'handle_click_tracking' ) );
		add_action( 'wp_ajax_houzez_ads_get_pricing', array( $this, 'handle_get_pricing' ) );
		add_action( 'wp_ajax_nopriv_houzez_ads_get_pricing', array( $this, 'handle_get_pricing' ) );

		// Campaign management AJAX handlers
		add_action( 'wp_ajax_houzez_ads_create_campaign', array( $this, 'handle_create_campaign' ) );
		add_action( 'wp_ajax_houzez_ads_delete_campaign', array( $this, 'handle_delete_campaign' ) );
		add_action( 'wp_ajax_houzez_ads_update_campaign_status', array( $this, 'handle_update_campaign_status' ) );

		// Frontend display hooks
		add_action( 'pre_get_posts', 'houzez_ads_insert_promoted_properties' );
		add_filter( 'the_posts', 'houzez_ads_inject_sponsored_properties', 10, 2 );

		// Houzez theme integration hooks
		add_action( 'houzez_before_property_loop', array( $this, 'display_search_zone_ads' ) );
		add_action( 'houzez_after_property_item', array( $this, 'display_property_detail_ads' ), 10, 2 );
		add_action( 'houzez_sidebar', array( $this, 'display_sidebar_ads' ) );
		add_action( 'wp_head', array( $this, 'add_sponsored_property_styles' ) );

		// Property loop hooks for promoted properties
		add_action( 'wp_head', array( $this, 'init_property_counter' ) );
		add_action( 'houzez_after_listing_item', array( $this, 'display_promoted_properties' ), 10 );

		// Handle click tracking redirects
		add_action( 'template_redirect', array( $this, 'handle_click_redirect' ) );
	}

	/**
	 * Display ad zone shortcode.
	 *
	 * @param array $atts Shortcode attributes.
	 * @return string Shortcode output.
	 */
	public function display_ad_zone( $atts ) {
		$atts = shortcode_atts( array(
			'zone' => 'homepage',
			'class' => '',
		), $atts, 'houzez_ads_zone' );

		$campaign = houzez_ads_get_rotated_campaign( $atts['zone'] );
		
		if ( ! $campaign ) {
			return '';
		}

		$banner_campaign = new Houzez_Banner_Campaign( $campaign );
		$html = $banner_campaign->get_display_html();

		if ( $atts['class'] ) {
			$html = str_replace( 'class="houzez-ad-campaign"', 'class="houzez-ad-campaign ' . esc_attr( $atts['class'] ) . '"', $html );
		}

		return $html;
	}

	/**
	 * Display campaign dashboard shortcode.
	 *
	 * @param array $atts Shortcode attributes.
	 * @return string Shortcode output.
	 */
	public function display_campaign_dashboard( $atts ) {
		if ( ! is_user_logged_in() ) {
			return '<p>' . __( 'Please log in to view your campaigns.', 'houzez-ads-extension' ) . '</p>';
		}

		if ( ! houzez_ads_user_can_create_campaigns() ) {
			return '<p>' . __( 'You do not have permission to create campaigns.', 'houzez-ads-extension' ) . '</p>';
		}

		ob_start();
		include HOUZEZ_ADS_EXTENSION_PATH . 'frontend/partials/campaign-dashboard.php';
		return ob_get_clean();
	}

	/**
	 * Display upload form shortcode.
	 *
	 * @param array $atts Shortcode attributes.
	 * @return string Shortcode output.
	 */
	public function display_upload_form( $atts ) {
		if ( ! is_user_logged_in() ) {
			return '<p>' . __( 'Please log in to create a campaign.', 'houzez-ads-extension' ) . '</p>';
		}

		if ( ! houzez_ads_user_can_create_campaigns() ) {
			return '<p>' . __( 'You do not have permission to create campaigns.', 'houzez-ads-extension' ) . '</p>';
		}

		ob_start();
		include HOUZEZ_ADS_EXTENSION_PATH . 'frontend/partials/upload-form.php';
		return ob_get_clean();
	}

	/**
	 * Handle click tracking AJAX request.
	 */
	public function handle_click_tracking() {
		check_ajax_referer( 'houzez_ads_tracking_nonce', 'nonce' );

		$campaign_id = absint( $_POST['campaign_id'] ?? 0 );
		
		if ( $campaign_id ) {
			houzez_ads_track_click( $campaign_id );
		}

		wp_die();
	}

	/**
	 * Handle get pricing AJAX request.
	 */
	public function handle_get_pricing() {
		check_ajax_referer( 'houzez_ads_tracking_nonce', 'nonce' );

		$ad_zone = sanitize_text_field( $_POST['zone'] ?? $_POST['ad_zone'] ?? '' );
		$duration = absint( $_POST['duration'] ?? 30 ); // Default to 30 days
		$quantity = absint( $_POST['quantity'] ?? 1 );
		$ad_type = sanitize_text_field( $_POST['ad_type'] ?? 'property' );
		$user_type = sanitize_text_field( $_POST['user_type'] ?? '' );

		// For regular users, force certain settings
		if ( $user_type === 'regular' ) {
			$ad_type = 'property';
			$ad_zone = 'search';
			$duration = 30; // Fixed 30 days for regular users
		}

		// For business users, force business partnership
		if ( $user_type === 'business' ) {
			$ad_type = 'partner';
		}

		if ( $ad_zone ) {
			$price = houzez_ads_calculate_campaign_price( $ad_zone, $duration, $quantity, $ad_type );
			wp_send_json_success( array(
				'price' => $price,
				'formatted_price' => houzez_ads_format_price( $price ),
				'debug' => array(
					'zone' => $ad_zone,
					'duration' => $duration,
					'quantity' => $quantity,
					'type' => $ad_type,
					'user_type' => $user_type
				)
			) );
		} else {
			wp_send_json_error( array(
				'message' => 'Invalid parameters',
				'debug' => array(
					'zone' => $ad_zone,
					'duration' => $duration,
					'user_type' => $user_type
				)
			) );
		}
	}

	/**
	 * Display search zone ads.
	 */
	public function display_search_zone_ads() {
		if ( is_search() || is_post_type_archive( 'property' ) ) {
			echo do_shortcode( '[houzez_ads_zone zone="search" class="houzez-ad-search"]' );
		}
	}

	/**
	 * Display property detail ads.
	 *
	 * @param int   $index Property index in loop.
	 * @param array $property Property data.
	 */
	public function display_property_detail_ads( $index, $property ) {
		// Display ad every 5th property
		if ( ( $index + 1 ) % 5 === 0 ) {
			echo do_shortcode( '[houzez_ads_zone zone="property_detail" class="houzez-ad-property-detail"]' );
		}
	}

	/**
	 * Display sidebar ads.
	 */
	public function display_sidebar_ads() {
		echo do_shortcode( '[houzez_ads_zone zone="sidebar" class="houzez-ad-sidebar"]' );
	}

	/**
	 * Add sponsored property styles to head.
	 */
	public function add_sponsored_property_styles() {
		?>
		<style>
		.property-item.sponsored {
			position: relative;
			border: 2px solid var(--houzez-primary, #f39c12) !important;
		}
		.property-item.sponsored::before {
			content: "<?php _e( 'Sponsored', 'houzez-ads-extension' ); ?>";
			position: absolute;
			top: 10px;
			right: 10px;
			background: var(--houzez-primary, #f39c12);
			color: #fff;
			padding: 4px 8px;
			border-radius: 4px;
			font-size: 11px;
			font-weight: 600;
			z-index: 10;
			text-transform: uppercase;
		}
		</style>
		<?php
	}

	/**
	 * Handle click tracking redirects.
	 */
	public function handle_click_redirect() {
		if ( isset( $_GET['houzez_ads_track'] ) && $_GET['houzez_ads_track'] === 'click' ) {
			$campaign_id = absint( $_GET['campaign_id'] ?? 0 );
			$redirect_url = urldecode( $_GET['redirect'] ?? '' );

			if ( $campaign_id && $redirect_url ) {
				// Track the click
				houzez_ads_track_click( $campaign_id );

				// Redirect to target URL
				wp_redirect( $redirect_url );
				exit;
			}
		}
	}

	/**
	 * Handle campaign creation AJAX request.
	 */
	public function handle_create_campaign() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['houzez_ads_nonce'], 'houzez_ads_upload_form' ) ) {
			wp_send_json_error( array( 'message' => __( 'Security check failed.', 'houzez-ads-extension' ) ) );
		}

		// Check permissions
		if ( ! is_user_logged_in() || ! houzez_ads_user_can_create_campaigns() ) {
			wp_send_json_error( array( 'message' => __( 'You do not have permission to create campaigns.', 'houzez-ads-extension' ) ) );
		}

		// Get user type and ad type for validation
		$user_type = houzez_ads_get_user_type();
		$ad_type = sanitize_text_field( $_POST['ad_type'] ?? '' );

		// Validate required fields based on user type and ad type
		$required_fields = array( 'ad_type', 'ad_zone' );

		// Duration is required for agency users only
		if ( $user_type === 'agency' ) {
			$required_fields[] = 'duration';
		}

		// Banner fields are required only for business partnership ads
		if ( $ad_type === 'partner' && $user_type === 'business' ) {
			$required_fields[] = 'banner_image';
			$required_fields[] = 'banner_link';
		}

		// Property selection is required for property ads
		if ( $ad_type === 'property' ) {
			if ( empty( $_POST['selected_properties'] ) || ! is_array( $_POST['selected_properties'] ) ) {
				wp_send_json_error( array( 'message' => __( 'Please select at least one property for promotion.', 'houzez-ads-extension' ) ) );
			}
		}

		foreach ( $required_fields as $field ) {
			if ( empty( $_POST[ $field ] ) ) {
				$field_label = str_replace( '_', ' ', $field );
				$field_label = ucwords( $field_label );

				// Custom labels for better UX
				if ( $field === 'banner_image' ) {
					$field_label = __( 'Banner Image URL', 'houzez-ads-extension' );
				} elseif ( $field === 'banner_link' ) {
					$field_label = __( 'Banner Link URL', 'houzez-ads-extension' );
				}

				wp_send_json_error( array( 'message' => sprintf( __( '%s is required.', 'houzez-ads-extension' ), $field_label ) ) );
			}
		}

		// Sanitize input data
		$campaign_data = array(
			'ad_type' => sanitize_text_field( $_POST['ad_type'] ),
			'ad_zone' => sanitize_text_field( $_POST['ad_zone'] ),
			'duration' => absint( $_POST['duration'] ),
			'banner_image' => esc_url_raw( $_POST['banner_image'] ),
			'banner_link' => esc_url_raw( $_POST['banner_link'] ),
			'banner_alt' => sanitize_text_field( $_POST['banner_alt'] ?? '' ),
			'banner_title' => sanitize_text_field( $_POST['banner_title'] ?? '' ),
		);

		// Handle property selection for property type ads
		if ( $campaign_data['ad_type'] === 'property' && ! empty( $_POST['selected_properties'] ) ) {
			$campaign_data['selected_properties'] = array_map( 'absint', $_POST['selected_properties'] );
		}

		try {
			// Create WooCommerce product and order
			if ( function_exists( 'houzez_ads_create_campaign_product' ) ) {
				$product_id = houzez_ads_create_campaign_product( $campaign_data );
				$order_id = houzez_ads_create_campaign_order( $product_id, $campaign_data );
				$campaign_data['order_id'] = $order_id;
			}

			// Create campaign post
			$campaign_id = wp_insert_post( array(
				'post_title' => sprintf( __( 'Campaign - %s - %s', 'houzez-ads-extension' ),
					ucfirst( $campaign_data['ad_zone'] ),
					date( 'Y-m-d H:i' )
				),
				'post_type' => 'banner_campaign',
				'post_status' => 'pending',
				'post_author' => get_current_user_id(),
			) );

			if ( is_wp_error( $campaign_id ) ) {
				wp_send_json_error( array( 'message' => __( 'Failed to create campaign.', 'houzez-ads-extension' ) ) );
			}

			// Save campaign meta data
			foreach ( $campaign_data as $key => $value ) {
				update_post_meta( $campaign_id, '_' . $key, $value );
			}

			// Set campaign status
			update_post_meta( $campaign_id, '_campaign_status', 'pending' );
			update_post_meta( $campaign_id, '_created_date', current_time( 'mysql' ) );

			wp_send_json_success( array(
				'message' => __( 'Campaign created successfully! It will be reviewed before going live.', 'houzez-ads-extension' ),
				'campaign_id' => $campaign_id,
				'redirect_url' => remove_query_arg( 'create_campaign' )
			) );

		} catch ( Exception $e ) {
			wp_send_json_error( array( 'message' => $e->getMessage() ) );
		}
	}

	/**
	 * Handle campaign deletion AJAX request.
	 */
	public function handle_delete_campaign() {
		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'], 'houzez_ads_delete_campaign' ) ) {
			wp_send_json_error( array( 'message' => __( 'Security check failed.', 'houzez-ads-extension' ) ) );
		}

		$campaign_id = absint( $_POST['campaign_id'] );
		if ( ! $campaign_id ) {
			wp_send_json_error( array( 'message' => __( 'Invalid campaign ID.', 'houzez-ads-extension' ) ) );
		}

		// Check if user owns the campaign or is admin
		$campaign = get_post( $campaign_id );
		if ( ! $campaign || $campaign->post_type !== 'banner_campaign' ) {
			wp_send_json_error( array( 'message' => __( 'Campaign not found.', 'houzez-ads-extension' ) ) );
		}

		if ( $campaign->post_author != get_current_user_id() && ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => __( 'You do not have permission to delete this campaign.', 'houzez-ads-extension' ) ) );
		}

		// Delete the campaign
		$deleted = wp_delete_post( $campaign_id, true );
		if ( $deleted ) {
			wp_send_json_success( array( 'message' => __( 'Campaign deleted successfully.', 'houzez-ads-extension' ) ) );
		} else {
			wp_send_json_error( array( 'message' => __( 'Failed to delete campaign.', 'houzez-ads-extension' ) ) );
		}
	}

	/**
	 * Handle campaign status update AJAX request.
	 */
	public function handle_update_campaign_status() {
		// Check admin permissions
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( array( 'message' => __( 'You do not have permission to update campaign status.', 'houzez-ads-extension' ) ) );
		}

		// Verify nonce
		if ( ! wp_verify_nonce( $_POST['nonce'], 'houzez_ads_update_status' ) ) {
			wp_send_json_error( array( 'message' => __( 'Security check failed.', 'houzez-ads-extension' ) ) );
		}

		$campaign_id = absint( $_POST['campaign_id'] );
		$new_status = sanitize_text_field( $_POST['status'] );

		if ( ! $campaign_id || ! in_array( $new_status, array( 'pending', 'approved', 'rejected', 'paused', 'expired' ) ) ) {
			wp_send_json_error( array( 'message' => __( 'Invalid parameters.', 'houzez-ads-extension' ) ) );
		}

		// Update campaign status
		update_post_meta( $campaign_id, '_campaign_status', $new_status );

		// Set start/end dates for approved campaigns
		if ( $new_status === 'approved' ) {
			$duration = get_post_meta( $campaign_id, '_duration', true );
			$start_date = current_time( 'mysql' );
			$end_date = date( 'Y-m-d H:i:s', strtotime( $start_date . ' +' . $duration . ' days' ) );

			update_post_meta( $campaign_id, '_start_date', $start_date );
			update_post_meta( $campaign_id, '_end_date', $end_date );
		}

		wp_send_json_success( array( 'message' => __( 'Campaign status updated successfully.', 'houzez-ads-extension' ) ) );
	}

	/**
	 * Display homepage agency profiles shortcode.
	 *
	 * @param array $atts Shortcode attributes.
	 * @return string Shortcode output.
	 */
	public function display_homepage_agencies( $atts ) {
		$atts = shortcode_atts( array(
			'limit' => 1,
			'class' => 'houzez-ads-homepage-agencies',
		), $atts, 'houzez_ads_homepage_agencies' );

		// Get active homepage campaigns for agencies
		$campaigns = $this->get_active_agency_campaigns( 'homepage', $atts['limit'] );

		if ( empty( $campaigns ) ) {
			return '';
		}

		$output = '<div class="' . esc_attr( $atts['class'] ) . '">';

		foreach ( $campaigns as $campaign ) {
			$output .= $this->render_agency_profile_card( $campaign );
		}

		$output .= '</div>';

		return $output;
	}

	/**
	 * Display sidebar agency profiles shortcode.
	 *
	 * @param array $atts Shortcode attributes.
	 * @return string Shortcode output.
	 */
	public function display_sidebar_agencies( $atts ) {
		$atts = shortcode_atts( array(
			'limit' => 3,
			'class' => 'houzez-ads-sidebar-agencies',
		), $atts, 'houzez_ads_sidebar_agencies' );

		// Get active sidebar campaigns for agencies
		$campaigns = $this->get_active_agency_campaigns( 'sidebar', $atts['limit'] );

		if ( empty( $campaigns ) ) {
			return '';
		}

		$output = '<div class="' . esc_attr( $atts['class'] ) . '">';

		foreach ( $campaigns as $campaign ) {
			$output .= $this->render_agency_profile_card( $campaign, 'sidebar' );
		}

		$output .= '</div>';

		return $output;
	}

	/**
	 * Display business partnership ad shortcode.
	 *
	 * @param array $atts Shortcode attributes.
	 * @return string Shortcode output.
	 */
	public function display_business_partnership_ad( $atts ) {
		$atts = shortcode_atts( array(
			'zone' => 'property_detail',
			'class' => 'houzez-ads-business-partnership',
		), $atts, 'houzez_ads_business_partnership' );

		// Get active business partnership campaigns
		$campaigns = $this->get_active_business_partnership_campaigns( $atts['zone'] );

		if ( empty( $campaigns ) ) {
			return '';
		}

		// Get random campaign for display
		$campaign = $campaigns[ array_rand( $campaigns ) ];
		$banner_campaign = new Houzez_Banner_Campaign( $campaign );

		$html = $banner_campaign->get_display_html();

		if ( $atts['class'] ) {
			$html = str_replace( 'class="houzez-ad-campaign"', 'class="houzez-ad-campaign ' . esc_attr( $atts['class'] ) . '"', $html );
		}

		return $html;
	}

	/**
	 * Get active agency campaigns for a specific zone.
	 *
	 * @param string $zone Ad zone.
	 * @param int $limit Number of campaigns to retrieve.
	 * @return array Array of campaign posts.
	 */
	private function get_active_agency_campaigns( $zone, $limit = 3 ) {
		$args = array(
			'post_type' => 'banner_campaign',
			'post_status' => 'publish',
			'posts_per_page' => $limit,
			'orderby' => 'rand',
			'meta_query' => array(
				'relation' => 'AND',
				array(
					'key' => '_houzez_ad_zone',
					'value' => $zone,
					'compare' => '='
				),
				array(
					'key' => '_houzez_ad_type',
					'value' => 'profile',
					'compare' => '='
				),
				array(
					'key' => '_houzez_campaign_status',
					'value' => 'approved',
					'compare' => '='
				),
				array(
					'key' => '_houzez_end_date',
					'value' => current_time( 'Y-m-d H:i:s' ),
					'compare' => '>='
				)
			)
		);

		return get_posts( $args );
	}

	/**
	 * Get active business partnership campaigns for a specific zone.
	 *
	 * @param string $zone Ad zone.
	 * @return array Array of campaign posts.
	 */
	private function get_active_business_partnership_campaigns( $zone ) {
		$args = array(
			'post_type' => 'banner_campaign',
			'post_status' => 'publish',
			'posts_per_page' => -1,
			'meta_query' => array(
				'relation' => 'AND',
				array(
					'key' => '_houzez_ad_zone',
					'value' => $zone,
					'compare' => '='
				),
				array(
					'key' => '_houzez_ad_type',
					'value' => 'partner',
					'compare' => '='
				),
				array(
					'key' => '_houzez_campaign_status',
					'value' => 'approved',
					'compare' => '='
				),
				array(
					'key' => '_houzez_end_date',
					'value' => current_time( 'Y-m-d H:i:s' ),
					'compare' => '>='
				)
			)
		);

		return get_posts( $args );
	}

	/**
	 * Render agency profile card.
	 *
	 * @param WP_Post $campaign Campaign post.
	 * @param string $style Card style (homepage or sidebar).
	 * @return string HTML output.
	 */
	private function render_agency_profile_card( $campaign, $style = 'homepage' ) {
		$user_id = $campaign->post_author;
		$user_data = get_userdata( $user_id );

		if ( ! $user_data ) {
			return '';
		}

		// Get user profile information
		$display_name = get_the_author_meta( 'display_name', $user_id );
		$user_title = get_the_author_meta( 'fave_author_title', $user_id );
		$user_company = get_the_author_meta( 'fave_author_company', $user_id );
		$user_phone = get_the_author_meta( 'fave_author_phone', $user_id );
		$user_email = get_the_author_meta( 'user_email', $user_id );
		$user_website = get_the_author_meta( 'url', $user_id );

		// Get profile picture
		$user_custom_picture = get_the_author_meta( 'fave_author_custom_picture', $user_id );
		$author_picture_id = get_the_author_meta( 'fave_author_picture_id', $user_id );

		if ( $author_picture_id ) {
			$profile_image = wp_get_attachment_image_url( $author_picture_id, 'thumbnail' );
		} elseif ( $user_custom_picture ) {
			$profile_image = $user_custom_picture;
		} else {
			$profile_image = get_avatar_url( $user_id, array( 'size' => 150 ) );
		}

		// Get user profile link
		$profile_link = '#';
		if ( function_exists( 'houzez_get_author_link' ) ) {
			$profile_link = houzez_get_author_link( $user_id );
		}

		// Track impression
		houzez_ads_track_impression( $campaign->ID );

		$card_class = $style === 'sidebar' ? 'houzez-agency-card-sidebar' : 'houzez-agency-card-homepage';

		ob_start();
		?>
		<div class="houzez-agency-card <?php echo esc_attr( $card_class ); ?>" data-campaign-id="<?php echo esc_attr( $campaign->ID ); ?>">
			<div class="agency-card-content">
				<div class="agency-avatar">
					<img src="<?php echo esc_url( $profile_image ); ?>" alt="<?php echo esc_attr( $display_name ); ?>" class="rounded-circle">
				</div>
				<div class="agency-info">
					<h5 class="agency-name">
						<a href="<?php echo esc_url( $profile_link ); ?>" target="_blank">
							<?php echo esc_html( $display_name ); ?>
						</a>
					</h5>
					<?php if ( $user_title || $user_company ) : ?>
						<p class="agency-title">
							<?php echo esc_html( $user_title ?: $user_company ); ?>
						</p>
					<?php endif; ?>
					<div class="agency-contact">
						<?php if ( $user_phone ) : ?>
							<span class="agency-phone">
								<i class="houzez-icon icon-phone"></i>
								<?php echo esc_html( $user_phone ); ?>
							</span>
						<?php endif; ?>
						<?php if ( $user_email ) : ?>
							<span class="agency-email">
								<i class="houzez-icon icon-envelope"></i>
								<a href="mailto:<?php echo esc_attr( $user_email ); ?>"><?php echo esc_html( $user_email ); ?></a>
							</span>
						<?php endif; ?>
					</div>
					<div class="agency-actions">
						<a href="<?php echo esc_url( $profile_link ); ?>" class="btn btn-primary btn-sm" target="_blank">
							<?php _e( 'View Profile', 'houzez-ads-extension' ); ?>
						</a>
						<?php if ( $user_website ) : ?>
							<a href="<?php echo esc_url( $user_website ); ?>" class="btn btn-outline-primary btn-sm" target="_blank">
								<?php _e( 'Website', 'houzez-ads-extension' ); ?>
							</a>
						<?php endif; ?>
					</div>
				</div>
			</div>
		</div>
		<?php
		return ob_get_clean();
	}

	/**
	 * Initialize property counter for promoted property display.
	 */
	public function init_property_counter() {
		if ( ! isset( $GLOBALS['houzez_ads_property_counter'] ) ) {
			$GLOBALS['houzez_ads_property_counter'] = 0;
		}
	}

	/**
	 * Display promoted properties after every 5 properties.
	 */
	public function display_promoted_properties() {
		// Only show on property search/listing pages
		if ( ! is_post_type_archive( 'property' ) && ! is_search() && ! is_tax() ) {
			return;
		}

		// Initialize counter if not set
		if ( ! isset( $GLOBALS['houzez_ads_property_counter'] ) ) {
			$GLOBALS['houzez_ads_property_counter'] = 0;
		}

		// Increment counter
		$GLOBALS['houzez_ads_property_counter']++;

		// Display promoted property after every 5 properties
		if ( $GLOBALS['houzez_ads_property_counter'] % 5 === 0 ) {
			$this->render_promoted_property_box();
		}
	}

	/**
	 * Render promoted property box.
	 */
	private function render_promoted_property_box() {
		// Get active property promotion campaigns
		$campaigns = $this->get_active_property_campaigns();

		if ( empty( $campaigns ) ) {
			return;
		}

		// Get random campaign
		$campaign = $campaigns[ array_rand( $campaigns ) ];
		$banner_campaign = new Houzez_Banner_Campaign( $campaign );

		// Get promoted properties from the campaign
		$promoted_properties = $banner_campaign->selected_properties;

		if ( empty( $promoted_properties ) || ! is_array( $promoted_properties ) ) {
			return;
		}

		// Get random property from the campaign
		$property_id = $promoted_properties[ array_rand( $promoted_properties ) ];
		$property = get_post( $property_id );

		if ( ! $property || $property->post_status !== 'publish' ) {
			return;
		}

		// Track impression
		houzez_ads_track_impression( $campaign->ID );

		// Render promoted property
		echo '<div class="houzez-promoted-property-wrapper" data-campaign-id="' . esc_attr( $campaign->ID ) . '">';

		// Set global post data for the promoted property
		global $post;
		$original_post = $post;
		$post = $property;
		setup_postdata( $post );

		// Add promoted class to the property item
		add_filter( 'post_class', array( $this, 'add_promoted_property_class' ) );

		// Get the current view template
		$current_view = $this->get_current_listing_view();
		get_template_part( 'template-parts/listing/item', $current_view );

		// Remove the filter
		remove_filter( 'post_class', array( $this, 'add_promoted_property_class' ) );

		// Restore original post data
		$post = $original_post;
		wp_reset_postdata();

		echo '</div>';
	}

	/**
	 * Add promoted class to property items.
	 *
	 * @param array $classes Post classes.
	 * @return array Modified classes.
	 */
	public function add_promoted_property_class( $classes ) {
		$classes[] = 'houzez-promoted-property';
		return $classes;
	}

	/**
	 * Get current listing view template.
	 *
	 * @return string Template name.
	 */
	private function get_current_listing_view() {
		// Try to get from URL parameters first
		if ( isset( $_GET['view'] ) ) {
			$view = sanitize_text_field( $_GET['view'] );
			if ( in_array( $view, array( 'v1', 'v2', 'v3', 'v4', 'v5', 'v6', 'v7' ) ) ) {
				return $view;
			}
		}

		// Get default view from theme options
		$default_view = houzez_option( 'search_result_posts_layout', 'list-view-v1' );

		// Extract template version from view setting
		if ( strpos( $default_view, 'grid-view-v' ) !== false ) {
			return str_replace( 'grid-view-', '', $default_view );
		} elseif ( strpos( $default_view, 'list-view-v' ) !== false ) {
			return str_replace( 'list-view-', '', $default_view );
		}

		// Default fallback
		return 'v1';
	}

	/**
	 * Get active property promotion campaigns.
	 *
	 * @return array Array of campaign posts.
	 */
	private function get_active_property_campaigns() {
		$args = array(
			'post_type' => 'banner_campaign',
			'post_status' => 'publish',
			'posts_per_page' => -1,
			'meta_query' => array(
				'relation' => 'AND',
				array(
					'key' => '_houzez_ad_type',
					'value' => 'property',
					'compare' => '='
				),
				array(
					'key' => '_houzez_campaign_status',
					'value' => 'approved',
					'compare' => '='
				),
				array(
					'key' => '_houzez_end_date',
					'value' => current_time( 'Y-m-d H:i:s' ),
					'compare' => '>='
				),
				array(
					'key' => '_houzez_selected_properties',
					'value' => '',
					'compare' => '!='
				)
			)
		);

		return get_posts( $args );
	}
}
