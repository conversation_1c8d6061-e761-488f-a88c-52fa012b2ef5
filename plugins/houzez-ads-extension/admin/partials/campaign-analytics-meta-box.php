<?php
/**
 * Campaign analytics meta box template
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/admin/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}
?>

<div class="houzez-ads-analytics">
	<?php if ( $campaign->id && $campaign->campaign_status === 'approved' ) : ?>
		<div class="houzez-ads-stat">
			<span class="houzez-ads-stat-number"><?php echo number_format( $analytics['impressions'] ); ?></span>
			<span class="houzez-ads-stat-label"><?php _e( 'Impressions', 'houzez-ads-extension' ); ?></span>
		</div>

		<div class="houzez-ads-stat">
			<span class="houzez-ads-stat-number"><?php echo number_format( $analytics['clicks'] ); ?></span>
			<span class="houzez-ads-stat-label"><?php _e( 'Clicks', 'houzez-ads-extension' ); ?></span>
		</div>

		<div class="houzez-ads-stat">
			<span class="houzez-ads-stat-number"><?php echo $analytics['ctr']; ?>%</span>
			<span class="houzez-ads-stat-label"><?php _e( 'Click-through Rate', 'houzez-ads-extension' ); ?></span>
		</div>

		<?php if ( $campaign->start_date && $campaign->end_date ) : ?>
			<div class="houzez-ads-stat">
				<span class="houzez-ads-stat-number">
					<?php 
					$days_remaining = ceil( ( strtotime( $campaign->end_date ) - current_time( 'timestamp' ) ) / ( 24 * 60 * 60 ) );
					echo max( 0, $days_remaining );
					?>
				</span>
				<span class="houzez-ads-stat-label"><?php _e( 'Days Remaining', 'houzez-ads-extension' ); ?></span>
			</div>
		<?php endif; ?>

		<?php if ( $campaign->order_id && houzez_ads_is_woocommerce_active() ) : ?>
			<div class="houzez-ads-field">
				<label><?php _e( 'Order', 'houzez-ads-extension' ); ?></label>
				<?php
				$order_edit_url = houzez_ads_get_order_edit_url( $campaign->order_id );
				if ( $order_edit_url ) {
					$order = wc_get_order( $campaign->order_id );
					?>
					<a href="<?php echo esc_url( $order_edit_url ); ?>" target="_blank">
						<?php printf( __( 'Order #%s', 'houzez-ads-extension' ), $order->get_order_number() ); ?>
					</a>
					<?php
				} else {
					printf( __( 'Order #%d (deleted)', 'houzez-ads-extension' ), $campaign->order_id );
				}
				?>
			</div>
		<?php endif; ?>
	<?php else : ?>
		<p><?php _e( 'Analytics will be available once the campaign is approved and running.', 'houzez-ads-extension' ); ?></p>
	<?php endif; ?>

	<?php if ( $campaign->campaign_status === 'rejected' ) : ?>
		<?php $rejection_reason = get_post_meta( $campaign->id, '_houzez_rejection_reason', true ); ?>
		<?php if ( $rejection_reason ) : ?>
			<div class="notice notice-error inline">
				<p><strong><?php _e( 'Rejection Reason:', 'houzez-ads-extension' ); ?></strong></p>
				<p><?php echo esc_html( $rejection_reason ); ?></p>
			</div>
		<?php endif; ?>
	<?php endif; ?>
</div>
