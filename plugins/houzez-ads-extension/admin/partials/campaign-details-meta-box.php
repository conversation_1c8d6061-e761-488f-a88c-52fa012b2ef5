<?php
/**
 * Campaign details meta box template
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/admin/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}
?>

<div class="houzez-ads-meta-box">
	<div class="houzez-ads-field">
		<label for="houzez_ad_type"><?php _e( 'Ad Type', 'houzez-ads-extension' ); ?></label>
		<select name="houzez_ad_type" id="houzez_ad_type">
			<option value=""><?php _e( 'Select Ad Type', 'houzez-ads-extension' ); ?></option>
			<?php foreach ( $ad_types as $key => $label ) : ?>
				<option value="<?php echo esc_attr( $key ); ?>" <?php selected( $campaign->ad_type, $key ); ?>>
					<?php echo esc_html( $label ); ?>
				</option>
			<?php endforeach; ?>
		</select>
	</div>

	<div class="houzez-ads-field">
		<label for="houzez_ad_zone"><?php _e( 'Ad Zone', 'houzez-ads-extension' ); ?></label>
		<select name="houzez_ad_zone" id="houzez_ad_zone">
			<option value=""><?php _e( 'Select Ad Zone', 'houzez-ads-extension' ); ?></option>
			<?php foreach ( $ad_zones as $key => $label ) : ?>
				<option value="<?php echo esc_attr( $key ); ?>" <?php selected( $campaign->ad_zone, $key ); ?>>
					<?php echo esc_html( $label ); ?>
				</option>
			<?php endforeach; ?>
		</select>
	</div>



	<div class="houzez-ads-field houzez-ads-property-selection" style="display: none;">
		<label><?php _e( 'Select Properties to Promote', 'houzez-ads-extension' ); ?></label>
		<div class="houzez-ads-properties-list">
			<?php if ( ! empty( $user_properties ) ) : ?>
				<?php foreach ( $user_properties as $property ) : ?>
					<div class="houzez-ads-property-item">
						<input type="checkbox" 
							   name="houzez_selected_properties[]" 
							   value="<?php echo esc_attr( $property->ID ); ?>"
							   id="property_<?php echo esc_attr( $property->ID ); ?>"
							   <?php checked( in_array( $property->ID, $campaign->selected_properties ) ); ?> />
						<label for="property_<?php echo esc_attr( $property->ID ); ?>">
							<?php echo esc_html( $property->post_title ); ?>
						</label>
					</div>
				<?php endforeach; ?>
			<?php else : ?>
				<p><?php _e( 'No properties found. Please create some properties first.', 'houzez-ads-extension' ); ?></p>
			<?php endif; ?>
		</div>
	</div>

	<div class="houzez-ads-field">
		<label for="houzez_campaign_status"><?php _e( 'Campaign Status', 'houzez-ads-extension' ); ?></label>
		<select name="houzez_campaign_status" id="houzez_campaign_status">
			<?php foreach ( $statuses as $key => $label ) : ?>
				<option value="<?php echo esc_attr( $key ); ?>" <?php selected( $campaign->campaign_status, $key ); ?>>
					<?php echo esc_html( $label ); ?>
				</option>
			<?php endforeach; ?>
		</select>
	</div>

	<div class="houzez-ads-field">
		<label>
			<input type="checkbox" id="use_custom_dates" name="use_custom_dates" value="1" <?php checked( !empty($campaign->start_date) || !empty($campaign->end_date) ); ?> />
			<?php _e( 'Use custom start/end dates (overrides duration)', 'houzez-ads-extension' ); ?>
		</label>
	</div>

	<div class="houzez-ads-custom-dates" style="<?php echo (!empty($campaign->start_date) || !empty($campaign->end_date)) ? '' : 'display: none;'; ?>">
		<div class="houzez-ads-field">
			<label for="houzez_start_date"><?php _e( 'Start Date', 'houzez-ads-extension' ); ?></label>
			<input type="datetime-local"
				   name="houzez_start_date"
				   id="houzez_start_date"
				   value="<?php echo esc_attr( $campaign->start_date ? date( 'Y-m-d\TH:i', strtotime( $campaign->start_date ) ) : '' ); ?>" />
		</div>

		<div class="houzez-ads-field">
			<label for="houzez_end_date"><?php _e( 'End Date', 'houzez-ads-extension' ); ?></label>
			<input type="datetime-local"
				   name="houzez_end_date"
				   id="houzez_end_date"
				   value="<?php echo esc_attr( $campaign->end_date ? date( 'Y-m-d\TH:i', strtotime( $campaign->end_date ) ) : '' ); ?>" />
		</div>
	</div>

	<?php if ( current_user_can( 'manage_options' ) ) : ?>
		<div class="notice notice-info inline" style="margin: 15px 0;">
			<p><strong><?php _e( 'Admin Note:', 'houzez-ads-extension' ); ?></strong> <?php _e( 'Campaigns created by administrators are automatically approved and do not require payment processing.', 'houzez-ads-extension' ); ?></p>
		</div>
	<?php endif; ?>

	<div class="houzez-ads-price-display" style="<?php echo $campaign->price ? '' : 'display: none;'; ?>">
		<span id="price-text">
			<?php if ( $campaign->price ) : ?>
				<?php printf( __( 'Campaign Price: %s', 'houzez-ads-extension' ), houzez_ads_format_price( $campaign->price ) ); ?>
			<?php else : ?>
				<?php _e( 'Select ad zone and duration to see pricing', 'houzez-ads-extension' ); ?>
			<?php endif; ?>
		</span>
	</div>
</div>
