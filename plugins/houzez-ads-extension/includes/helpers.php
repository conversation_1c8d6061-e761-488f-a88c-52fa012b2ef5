<?php
/**
 * Helper functions for Houzez Ads Extension
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/includes
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Get user IP address.
 *
 * @return string User IP address.
 */
if ( ! function_exists( 'houzez_ads_get_user_ip' ) ) {
	function houzez_ads_get_user_ip() {
		$ip = '';

		if ( ! empty( $_SERVER['HTTP_CLIENT_IP'] ) ) {
			$ip = $_SERVER['HTTP_CLIENT_IP'];
		} elseif ( ! empty( $_SERVER['HTTP_X_FORWARDED_FOR'] ) ) {
			$ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
		} elseif ( ! empty( $_SERVER['REMOTE_ADDR'] ) ) {
			$ip = $_SERVER['REMOTE_ADDR'];
		}

		return sanitize_text_field( $ip );
	}
}

/**
 * Check if user can create campaigns.
 *
 * @param int $user_id User ID (optional, defaults to current user).
 * @return bool True if user can create campaigns.
 */
if ( ! function_exists( 'houzez_ads_user_can_create_campaigns' ) ) {
	function houzez_ads_user_can_create_campaigns( $user_id = 0 ) {
		if ( ! $user_id ) {
			$user_id = get_current_user_id();
		}

		if ( ! $user_id ) {
			return false;
		}

		// Check if user has required capabilities
		$user = get_user_by( 'id', $user_id );
		if ( ! $user ) {
			return false;
		}

		// Get allowed roles from admin settings or use defaults
		$default_roles = array(
			'administrator',
			'editor',
			'author',
			'contributor',
			'houzez_agent',
			'houzez_agency',
			'houzez_buyer',
			'houzez_seller',
			'houzez_owner',
			'houzez_manager'
		);

		$allowed_roles = get_option( 'houzez_ads_allowed_roles', $default_roles );

		// Apply filter to allow customization
		$allowed_roles = apply_filters( 'houzez_ads_allowed_roles', $allowed_roles );

		$user_roles = $user->roles;

		return ! empty( array_intersect( $allowed_roles, $user_roles ) );
	}
}

/**
 * Get user's properties for campaign selection.
 *
 * @param int $user_id User ID (optional, defaults to current user).
 * @return array Array of user's properties.
 */
if ( ! function_exists( 'houzez_ads_get_user_properties' ) ) {
	function houzez_ads_get_user_properties( $user_id = 0 ) {
		if ( ! $user_id ) {
			$user_id = get_current_user_id();
		}

		$args = array(
			'post_type' => 'property',
			'post_status' => 'publish',
			'author' => $user_id,
			'posts_per_page' => -1,
			'orderby' => 'title',
			'order' => 'ASC'
		);

		return get_posts( $args );
	}
}

/**
 * Check if campaign is active.
 *
 * @param int $campaign_id Campaign ID.
 * @return bool True if campaign is active.
 */
function houzez_ads_is_campaign_active( $campaign_id ) {
	$status = get_post_meta( $campaign_id, '_houzez_campaign_status', true );
	$end_date = get_post_meta( $campaign_id, '_houzez_end_date', true );

	if ( $status !== 'approved' ) {
		return false;
	}

	if ( $end_date && strtotime( $end_date ) < current_time( 'timestamp' ) ) {
		return false;
	}

	return true;
}

/**
 * Get campaign analytics data.
 *
 * @param int $campaign_id Campaign ID.
 * @return array Analytics data.
 */
function houzez_ads_get_campaign_analytics( $campaign_id ) {
	global $wpdb;

	$table_name = $wpdb->prefix . 'houzez_ad_analytics';

	// Get impressions count
	$impressions = $wpdb->get_var( $wpdb->prepare(
		"SELECT COUNT(*) FROM $table_name WHERE campaign_id = %d AND event_type = 'impression'",
		$campaign_id
	) );

	// Get clicks count
	$clicks = $wpdb->get_var( $wpdb->prepare(
		"SELECT COUNT(*) FROM $table_name WHERE campaign_id = %d AND event_type = 'click'",
		$campaign_id
	) );

	// Calculate CTR
	$ctr = 0;
	if ( $impressions > 0 ) {
		$ctr = ( $clicks / $impressions ) * 100;
	}

	return array(
		'impressions' => (int) $impressions,
		'clicks' => (int) $clicks,
		'ctr' => round( $ctr, 2 )
	);
}

/**
 * Generate campaign tracking URL.
 *
 * @param int    $campaign_id Campaign ID.
 * @param string $target_url  Target URL.
 * @return string Tracking URL.
 */
function houzez_ads_generate_tracking_url( $campaign_id, $target_url ) {
	$tracking_url = add_query_arg( array(
		'houzez_ads_track' => 'click',
		'campaign_id' => $campaign_id,
		'redirect' => urlencode( $target_url )
	), home_url( '/' ) );

	return $tracking_url;
}

/**
 * Sanitize campaign data.
 *
 * @param array $data Campaign data.
 * @return array Sanitized data.
 */
function houzez_ads_sanitize_campaign_data( $data ) {
	$sanitized = array();

	if ( isset( $data['ad_type'] ) ) {
		$sanitized['ad_type'] = sanitize_text_field( $data['ad_type'] );
	}

	if ( isset( $data['ad_zone'] ) ) {
		$sanitized['ad_zone'] = sanitize_text_field( $data['ad_zone'] );
	}

	if ( isset( $data['duration'] ) ) {
		$sanitized['duration'] = absint( $data['duration'] );
	}

	if ( isset( $data['selected_properties'] ) && is_array( $data['selected_properties'] ) ) {
		$sanitized['selected_properties'] = array_map( 'absint', $data['selected_properties'] );
	}

	if ( isset( $data['banner_image'] ) ) {
		$sanitized['banner_image'] = esc_url_raw( $data['banner_image'] );
	}

	if ( isset( $data['banner_link'] ) ) {
		$sanitized['banner_link'] = esc_url_raw( $data['banner_link'] );
	}

	if ( isset( $data['banner_alt'] ) ) {
		$sanitized['banner_alt'] = sanitize_text_field( $data['banner_alt'] );
	}

	return $sanitized;
}

/**
 * Get campaign status options.
 *
 * @return array Status options.
 */
function houzez_ads_get_campaign_statuses() {
	return array(
		'pending' => __( 'Pending Review', 'houzez-ads-extension' ),
		'approved' => __( 'Approved', 'houzez-ads-extension' ),
		'rejected' => __( 'Rejected', 'houzez-ads-extension' ),
		'expired' => __( 'Expired', 'houzez-ads-extension' ),
		'paused' => __( 'Paused', 'houzez-ads-extension' )
	);
}

/**
 * Check if Houzez theme is active.
 *
 * @return bool True if Houzez theme is active.
 */
if ( ! function_exists( 'houzez_ads_is_houzez_theme_active' ) ) {
	function houzez_ads_is_houzez_theme_active() {
		$theme = wp_get_theme();
		return ( $theme->get( 'Name' ) === 'Houzez' || $theme->get( 'Template' ) === 'houzez' );
	}
}

/**
 * Get Houzez dashboard URL.
 *
 * @return string Dashboard URL.
 */
if ( ! function_exists( 'houzez_ads_get_dashboard_url' ) ) {
	function houzez_ads_get_dashboard_url() {
		// First try to get the dashboard page we created
		$page_id = get_option( 'houzez_ads_dashboard_page_id' );
		if ( $page_id ) {
			$page_url = get_permalink( $page_id );
			if ( $page_url ) {
				return $page_url;
			}
		}

		// Fallback to Houzez template link if available
		if ( function_exists( 'houzez_get_template_link_2' ) ) {
			return houzez_get_template_link_2( 'template/user_dashboard_ad_campaign.php' );
		}

		return home_url( '/dashboard/' );
	}
}



/**
 * Check if WooCommerce is active and HPOS is enabled.
 *
 * @return bool True if WooCommerce is active.
 */
function houzez_ads_is_woocommerce_active() {
	return class_exists( 'WooCommerce' );
}

/**
 * Check if WooCommerce HPOS is enabled.
 *
 * @return bool True if HPOS is enabled.
 */
function houzez_ads_is_hpos_enabled() {
	if ( ! houzez_ads_is_woocommerce_active() ) {
		return false;
	}

	if ( class_exists( '\Automattic\WooCommerce\Utilities\OrderUtil' ) ) {
		return \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled();
	}

	return false;
}

/**
 * Get order edit URL compatible with HPOS.
 *
 * @param int $order_id Order ID.
 * @return string Order edit URL.
 */
function houzez_ads_get_order_edit_url( $order_id ) {
	if ( ! houzez_ads_is_woocommerce_active() ) {
		return '';
	}

	$order = wc_get_order( $order_id );
	if ( ! $order ) {
		return '';
	}

	return $order->get_edit_order_url();
}

/**
 * Get user's credit balance.
 *
 * @param int $user_id User ID (optional, defaults to current user).
 * @return int User's credit balance.
 */
if ( ! function_exists( 'houzez_ads_get_user_credits' ) ) {
	function houzez_ads_get_user_credits( $user_id = 0 ) {
		if ( ! $user_id ) {
			$user_id = get_current_user_id();
		}

		if ( ! $user_id ) {
			return 0;
		}

		$credits = get_user_meta( $user_id, 'houzez_ads_credits', true );
		return absint( $credits );
	}
}

/**
 * Add credits to user's balance.
 *
 * @param int $user_id User ID.
 * @param int $credits Credits to add.
 * @param string $reason Reason for credit addition.
 * @return bool True on success, false on failure.
 */
if ( ! function_exists( 'houzez_ads_add_user_credits' ) ) {
	function houzez_ads_add_user_credits( $user_id, $credits, $reason = '' ) {
		if ( ! $user_id || $credits <= 0 ) {
			return false;
		}

		$current_credits = houzez_ads_get_user_credits( $user_id );
		$new_balance = $current_credits + absint( $credits );

		$result = update_user_meta( $user_id, 'houzez_ads_credits', $new_balance );

		if ( $result ) {
			// Log the transaction
			houzez_ads_log_credit_transaction( $user_id, $credits, 'add', $reason, $new_balance );
		}

		return $result;
	}
}

/**
 * Deduct credits from user's balance.
 *
 * @param int $user_id User ID.
 * @param int $credits Credits to deduct.
 * @param string $reason Reason for credit deduction.
 * @return bool True on success, false on failure.
 */
if ( ! function_exists( 'houzez_ads_deduct_user_credits' ) ) {
	function houzez_ads_deduct_user_credits( $user_id, $credits, $reason = '' ) {
		if ( ! $user_id || $credits <= 0 ) {
			return false;
		}

		$current_credits = houzez_ads_get_user_credits( $user_id );

		if ( $current_credits < $credits ) {
			return false; // Insufficient credits
		}

		$new_balance = $current_credits - absint( $credits );

		$result = update_user_meta( $user_id, 'houzez_ads_credits', $new_balance );

		if ( $result ) {
			// Log the transaction
			houzez_ads_log_credit_transaction( $user_id, $credits, 'deduct', $reason, $new_balance );
		}

		return $result;
	}
}

/**
 * Check if user has sufficient credits.
 *
 * @param int $user_id User ID.
 * @param int $required_credits Required credits.
 * @return bool True if user has sufficient credits.
 */
if ( ! function_exists( 'houzez_ads_user_has_credits' ) ) {
	function houzez_ads_user_has_credits( $user_id, $required_credits ) {
		$current_credits = houzez_ads_get_user_credits( $user_id );
		return $current_credits >= $required_credits;
	}
}

/**
 * Log credit transaction.
 *
 * @param int $user_id User ID.
 * @param int $credits Credits amount.
 * @param string $type Transaction type (add/deduct).
 * @param string $reason Transaction reason.
 * @param int $balance_after Balance after transaction.
 * @return bool True on success.
 */
if ( ! function_exists( 'houzez_ads_log_credit_transaction' ) ) {
	function houzez_ads_log_credit_transaction( $user_id, $credits, $type, $reason, $balance_after ) {
		global $wpdb;

		$table_name = $wpdb->prefix . 'houzez_ads_credit_transactions';

		$result = $wpdb->insert(
			$table_name,
			array(
				'user_id' => $user_id,
				'credits' => $credits,
				'transaction_type' => $type,
				'reason' => $reason,
				'balance_after' => $balance_after,
				'created_at' => current_time( 'mysql' )
			),
			array( '%d', '%d', '%s', '%s', '%d', '%s' )
		);

		return $result !== false;
	}
}

/**
 * Get user's credit transaction history.
 *
 * @param int $user_id User ID.
 * @param int $limit Number of transactions to retrieve.
 * @return array Transaction history.
 */
if ( ! function_exists( 'houzez_ads_get_user_credit_history' ) ) {
	function houzez_ads_get_user_credit_history( $user_id, $limit = 20 ) {
		global $wpdb;

		$table_name = $wpdb->prefix . 'houzez_ads_credit_transactions';

		$results = $wpdb->get_results( $wpdb->prepare(
			"SELECT * FROM $table_name WHERE user_id = %d ORDER BY created_at DESC LIMIT %d",
			$user_id,
			$limit
		) );

		return $results ? $results : array();
	}
}

/**
 * Format credits for display.
 *
 * @param int $credits Credits amount.
 * @return string Formatted credits.
 */
if ( ! function_exists( 'houzez_ads_format_credits' ) ) {
	function houzez_ads_format_credits( $credits ) {
		return number_format( absint( $credits ) ) . ' ' . __( 'Credits', 'houzez-ads-extension' );
	}
}

/**
 * Assign dummy credits to all users for testing.
 *
 * @param int $credits Credits to assign.
 * @return int Number of users updated.
 */
if ( ! function_exists( 'houzez_ads_assign_dummy_credits' ) ) {
	function houzez_ads_assign_dummy_credits( $credits = 1000 ) {
		$users = get_users( array( 'fields' => 'ID' ) );
		$updated_count = 0;

		foreach ( $users as $user_id ) {
			if ( houzez_ads_add_user_credits( $user_id, $credits, 'Dummy credits for testing' ) ) {
				$updated_count++;
			}
		}

		return $updated_count;
	}
}

/**
 * Track impression for analytics.
 *
 * @param int $campaign_id Campaign ID.
 * @return bool True on success.
 */
if ( ! function_exists( 'houzez_ads_track_impression' ) ) {
	function houzez_ads_track_impression( $campaign_id ) {
		global $wpdb;

		$table_name = $wpdb->prefix . 'houzez_ad_analytics';

		$result = $wpdb->insert(
			$table_name,
			array(
				'campaign_id' => $campaign_id,
				'event_type' => 'impression',
				'user_id' => get_current_user_id(),
				'ip_address' => houzez_ads_get_user_ip(),
				'user_agent' => isset( $_SERVER['HTTP_USER_AGENT'] ) ? $_SERVER['HTTP_USER_AGENT'] : '',
				'referrer' => isset( $_SERVER['HTTP_REFERER'] ) ? $_SERVER['HTTP_REFERER'] : '',
				'created_at' => current_time( 'mysql' )
			),
			array( '%d', '%s', '%d', '%s', '%s', '%s', '%s' )
		);

		return $result !== false;
	}
}

/**
 * Track click for analytics.
 *
 * @param int $campaign_id Campaign ID.
 * @return bool True on success.
 */
if ( ! function_exists( 'houzez_ads_track_click' ) ) {
	function houzez_ads_track_click( $campaign_id ) {
		global $wpdb;

		$table_name = $wpdb->prefix . 'houzez_ad_analytics';

		$result = $wpdb->insert(
			$table_name,
			array(
				'campaign_id' => $campaign_id,
				'event_type' => 'click',
				'user_id' => get_current_user_id(),
				'ip_address' => houzez_ads_get_user_ip(),
				'user_agent' => isset( $_SERVER['HTTP_USER_AGENT'] ) ? $_SERVER['HTTP_USER_AGENT'] : '',
				'referrer' => isset( $_SERVER['HTTP_REFERER'] ) ? $_SERVER['HTTP_REFERER'] : '',
				'created_at' => current_time( 'mysql' )
			),
			array( '%d', '%s', '%d', '%s', '%s', '%s', '%s' )
		);

		return $result !== false;
	}
}

/**
 * Clear rotation cache for a specific zone.
 *
 * @param string $zone Ad zone.
 * @return bool True on success.
 */
if ( ! function_exists( 'houzez_ads_clear_rotation_cache' ) ) {
	function houzez_ads_clear_rotation_cache( $zone ) {
		return wp_cache_delete( "houzez_ads_rotation_{$zone}", 'houzez_ads' );
	}
}

/**
 * Get user type for ads system.
 *
 * @param int $user_id User ID (optional, defaults to current user).
 * @return string User type: 'agency', 'regular', or 'business'.
 */
if ( ! function_exists( 'houzez_ads_get_user_type' ) ) {
	function houzez_ads_get_user_type( $user_id = null ) {
		if ( ! $user_id ) {
			$user_id = get_current_user_id();
		}

		if ( ! $user_id ) {
			return 'regular';
		}

		$user = get_userdata( $user_id );
		if ( ! $user ) {
			return 'regular';
		}

		$roles = (array) $user->roles;

		// Check if user is agency or agent (both can create profile ads)
		if ( in_array( 'houzez_agency', $roles ) || in_array( 'houzez_agent', $roles ) ) {
			return 'agency';
		}

		// Check if user is business/contractor (can create partnership ads)
		// You can customize this logic based on how you identify business users
		// For now, we'll use a meta field or specific role
		$is_business = get_user_meta( $user_id, 'houzez_ads_user_type', true );
		if ( $is_business === 'business' ) {
			return 'business';
		}

		// Also check for specific business roles if you have them
		if ( in_array( 'houzez_business', $roles ) || in_array( 'contractor', $roles ) ) {
			return 'business';
		}

		// Default to regular user (buyers, sellers, owners, etc.)
		return 'regular';
	}
}

/**
 * Check if user is agency type.
 *
 * @param int $user_id User ID (optional).
 * @return bool True if agency type.
 */
if ( ! function_exists( 'houzez_ads_is_agency_user' ) ) {
	function houzez_ads_is_agency_user( $user_id = null ) {
		return houzez_ads_get_user_type( $user_id ) === 'agency';
	}
}

/**
 * Check if user is business type.
 *
 * @param int $user_id User ID (optional).
 * @return bool True if business type.
 */
if ( ! function_exists( 'houzez_ads_is_business_user' ) ) {
	function houzez_ads_is_business_user( $user_id = null ) {
		return houzez_ads_get_user_type( $user_id ) === 'business';
	}
}

/**
 * Check if user is regular type.
 *
 * @param int $user_id User ID (optional).
 * @return bool True if regular type.
 */
if ( ! function_exists( 'houzez_ads_is_regular_user' ) ) {
	function houzez_ads_is_regular_user( $user_id = null ) {
		return houzez_ads_get_user_type( $user_id ) === 'regular';
	}
}

/**
 * Get available ad types based on user type.
 *
 * @param int $user_id User ID (optional).
 * @return array Available ad types for the user.
 */
if ( ! function_exists( 'houzez_ads_get_available_ad_types' ) ) {
	function houzez_ads_get_available_ad_types( $user_id = null ) {
		$user_type = houzez_ads_get_user_type( $user_id );

		switch ( $user_type ) {
			case 'agency':
				return array(
					'profile' => __( 'Agency/Profile Promotion', 'houzez-ads-extension' ),
					'property' => __( 'Property Promotion', 'houzez-ads-extension' )
				);

			case 'business':
				return array(
					'partner' => __( 'Business Partnership', 'houzez-ads-extension' )
				);

			case 'regular':
			default:
				return array(
					'property' => __( 'Property Promotion', 'houzez-ads-extension' )
				);
		}
	}
}

/**
 * Get available ad zones based on user type and ad type.
 *
 * @param string $ad_type Ad type.
 * @param int $user_id User ID (optional).
 * @return array Available zones.
 */
if ( ! function_exists( 'houzez_ads_get_available_zones_for_user' ) ) {
	function houzez_ads_get_available_zones_for_user( $ad_type = '', $user_id = null ) {
		$user_type = houzez_ads_get_user_type( $user_id );

		// For agency users with profile ads
		if ( $user_type === 'agency' && $ad_type === 'profile' ) {
			return array(
				'homepage' => __( 'Homepage Banner', 'houzez-ads-extension' ),
				'sidebar' => __( 'Sidebar Ads', 'houzez-ads-extension' )
			);
		}

		// For regular users with property ads (fixed to search results)
		if ( $user_type === 'regular' && $ad_type === 'property' ) {
			return array(
				'search' => __( 'Search Results', 'houzez-ads-extension' )
			);
		}

		// For agency users with property ads (all zones)
		if ( $user_type === 'agency' && $ad_type === 'property' ) {
			return array(
				'homepage' => __( 'Homepage Banner', 'houzez-ads-extension' ),
				'sidebar' => __( 'Sidebar Ads', 'houzez-ads-extension' ),
				'search' => __( 'Search Results', 'houzez-ads-extension' ),
				'property_detail' => __( 'Property Detail Page', 'houzez-ads-extension' )
			);
		}

		// For business users (all zones)
		if ( $user_type === 'business' ) {
			return array(
				'homepage' => __( 'Homepage Banner', 'houzez-ads-extension' ),
				'sidebar' => __( 'Sidebar Ads', 'houzez-ads-extension' ),
				'search' => __( 'Search Results', 'houzez-ads-extension' ),
				'property_detail' => __( 'Property Detail Page', 'houzez-ads-extension' )
			);
		}

		// Default fallback
		return array(
			'search' => __( 'Search Results', 'houzez-ads-extension' )
		);
	}
}

/**
 * Get available ad zones.
 *
 * @return array Available ad zones.
 */
if ( ! function_exists( 'houzez_ads_get_available_zones' ) ) {
	function houzez_ads_get_available_zones() {
		return array(
			'homepage' => __( 'Homepage Banner', 'houzez-ads-extension' ),
			'sidebar' => __( 'Sidebar Ads', 'houzez-ads-extension' ),
			'search' => __( 'Search Results', 'houzez-ads-extension' ),
			'property_detail' => __( 'Property Detail Page', 'houzez-ads-extension' )
		);
	}
}

/**
 * Get available campaign durations.
 *
 * @return array Available durations.
 */
if ( ! function_exists( 'houzez_ads_get_available_durations' ) ) {
	function houzez_ads_get_available_durations() {
		return array(
			'7' => __( '7 Days', 'houzez-ads-extension' ),
			'14' => __( '14 Days', 'houzez-ads-extension' ),
			'30' => __( '30 Days', 'houzez-ads-extension' )
		);
	}
}