<?php
/**
 * Dynamic pricing functionality for Houzez Ads Extension
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/includes
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Calculate campaign price in credits based on zone, duration, and quantity.
 *
 * @param string $ad_zone The ad zone (homepage, sidebar, search, property_detail).
 * @param int    $duration Duration in days (7, 14, 30).
 * @param int    $quantity Number of properties (for property-specific ads).
 * @param string $ad_type The ad type (property, profile, partner).
 * @return int The calculated price in credits.
 */
if ( ! function_exists( 'houzez_ads_calculate_campaign_price' ) ) {
	function houzez_ads_calculate_campaign_price( $ad_zone, $duration, $quantity = 1, $ad_type = 'property' ) {
	$pricing = get_option( 'houzez_ads_credit_pricing', array() );

	// Default credit pricing if not set
	if ( empty( $pricing ) ) {
		$pricing = array(
			'homepage' => array(
				'7' => 49,
				'14' => 89,
				'30' => 149
			),
			'sidebar' => array(
				'7' => 29,
				'14' => 49,
				'30' => 89
			),
			'search' => array(
				'7' => 10,
				'14' => 18,
				'30' => 30
			),
			'property_detail' => array(
				'7' => 35,
				'14' => 59,
				'30' => 99
			)
		);
	}

	// Get base price for zone and duration
	$base_price = 0;
	if ( isset( $pricing[ $ad_zone ][ $duration ] ) ) {
		$base_price = $pricing[ $ad_zone ][ $duration ];
	}

	// Apply quantity multiplier for property-specific ads
	if ( $ad_type === 'property' && $ad_zone === 'search' ) {
		$base_price = $base_price * $quantity;
	}

	// Apply discounts for longer durations
	$discount = 0;
	if ( $duration >= 30 ) {
		$discount = 0.15; // 15% discount for 30+ days
	} elseif ( $duration >= 14 ) {
		$discount = 0.10; // 10% discount for 14+ days
	}

	$final_price = $base_price * ( 1 - $discount );

	/**
	 * Filter the calculated campaign price in credits.
	 *
	 * @param int    $final_price The calculated price in credits.
	 * @param string $ad_zone     The ad zone.
	 * @param int    $duration    Duration in days.
	 * @param int    $quantity    Number of properties.
	 * @param string $ad_type     The ad type.
	 */
		return absint( apply_filters( 'houzez_ads_campaign_price', $final_price, $ad_zone, $duration, $quantity, $ad_type ) );
	}
}

/**
 * Get credit pricing table for display.
 *
 * @return array The credit pricing table.
 */
if ( ! function_exists( 'houzez_ads_get_pricing_table' ) ) {
	function houzez_ads_get_pricing_table() {
		return get_option( 'houzez_ads_credit_pricing', array() );
	}
}

/**
 * Update credit pricing table.
 *
 * @param array $pricing The new credit pricing table.
 * @return bool True on success, false on failure.
 */
if ( ! function_exists( 'houzez_ads_update_pricing_table' ) ) {
	function houzez_ads_update_pricing_table( $pricing ) {
		return update_option( 'houzez_ads_credit_pricing', $pricing );
	}
}

/**
 * Get formatted price for display in credits.
 *
 * @param int $credits The credits amount to format.
 * @return string The formatted credits.
 */
if ( ! function_exists( 'houzez_ads_format_price' ) ) {
	function houzez_ads_format_price( $credits ) {
		return houzez_ads_format_credits( $credits );
	}
}

// Note: Functions houzez_ads_get_available_zones(), houzez_ads_get_available_durations(),
// and houzez_ads_get_available_ad_types() are defined in helpers.php to avoid duplication.

