<?php
/**
 * <PERSON><PERSON>z Banner Campaign model class
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/models
 */

/**
 * Houzez Banner Campaign model.
 *
 * Handles banner campaign data and operations for Houzez theme integration.
 */
class Houzez_Banner_Campaign {

	/**
	 * Campaign ID.
	 *
	 * @var int
	 */
	public $id;

	/**
	 * Campaign title.
	 *
	 * @var string
	 */
	public $title;

	/**
	 * Campaign status.
	 *
	 * @var string
	 */
	public $status;

	/**
	 * Campaign owner user ID.
	 *
	 * @var int
	 */
	public $user_id;

	/**
	 * Ad type (property, profile, partner).
	 *
	 * @var string
	 */
	public $ad_type;

	/**
	 * Ad zone (homepage, sidebar, search, property_detail).
	 *
	 * @var string
	 */
	public $ad_zone;

	/**
	 * Campaign duration in days.
	 *
	 * @var int
	 */
	public $duration;

	/**
	 * Selected properties for property-type campaigns.
	 *
	 * @var array
	 */
	public $selected_properties;

	/**
	 * Banner image URL.
	 *
	 * @var string
	 */
	public $banner_image;

	/**
	 * Banner link URL.
	 *
	 * @var string
	 */
	public $banner_link;

	/**
	 * Banner alt text.
	 *
	 * @var string
	 */
	public $banner_alt;

	/**
	 * Campaign start date.
	 *
	 * @var string
	 */
	public $start_date;

	/**
	 * Campaign end date.
	 *
	 * @var string
	 */
	public $end_date;

	/**
	 * Campaign approval status.
	 *
	 * @var string
	 */
	public $campaign_status;

	/**
	 * Campaign price in credits.
	 *
	 * @var int
	 */
	public $price;

	/**
	 * WooCommerce order ID.
	 *
	 * @var int
	 */
	public $order_id;

	/**
	 * Constructor.
	 *
	 * @param int|WP_Post $campaign Campaign ID or post object.
	 */
	public function __construct( $campaign = 0 ) {
		if ( $campaign instanceof WP_Post ) {
			$this->id = $campaign->ID;
			$this->load_campaign_data( $campaign );
		} elseif ( is_numeric( $campaign ) && $campaign > 0 ) {
			$this->id = $campaign;
			$post = get_post( $campaign );
			if ( $post && $post->post_type === 'banner_campaign' ) {
				$this->load_campaign_data( $post );
			}
		}
	}

	/**
	 * Load campaign data from post.
	 *
	 * @param WP_Post $post Campaign post object.
	 */
	private function load_campaign_data( $post ) {
		$this->title = $post->post_title;
		$this->status = $post->post_status;
		$this->user_id = $post->post_author;

		// Load meta data
		$this->ad_type = get_post_meta( $this->id, '_houzez_ad_type', true );
		$this->ad_zone = get_post_meta( $this->id, '_houzez_ad_zone', true );
		$this->duration = get_post_meta( $this->id, '_houzez_duration', true );
		$this->selected_properties = get_post_meta( $this->id, '_houzez_selected_properties', true );
		$this->banner_image = get_post_meta( $this->id, '_houzez_banner_image', true );
		$this->banner_link = get_post_meta( $this->id, '_houzez_banner_link', true );
		$this->banner_alt = get_post_meta( $this->id, '_houzez_banner_alt', true );
		$this->start_date = get_post_meta( $this->id, '_houzez_start_date', true );
		$this->end_date = get_post_meta( $this->id, '_houzez_end_date', true );
		$this->campaign_status = get_post_meta( $this->id, '_houzez_campaign_status', true );
		$this->price = get_post_meta( $this->id, '_houzez_campaign_price', true );
		$this->order_id = get_post_meta( $this->id, '_houzez_order_id', true );

		// Ensure arrays are properly formatted
		if ( ! is_array( $this->selected_properties ) ) {
			$this->selected_properties = array();
		}
	}

	/**
	 * Save campaign data.
	 *
	 * @param array $data Campaign data to save.
	 * @return bool|WP_Error True on success, WP_Error on failure.
	 */
	public function save( $data = array() ) {
		// Sanitize data
		$data = houzez_ads_sanitize_campaign_data( $data );

		// Update post if ID exists
		if ( $this->id ) {
			$post_data = array(
				'ID' => $this->id,
				'post_title' => isset( $data['title'] ) ? $data['title'] : $this->title,
				'post_status' => isset( $data['status'] ) ? $data['status'] : $this->status,
			);

			$result = wp_update_post( $post_data );
			if ( is_wp_error( $result ) ) {
				return $result;
			}
		} else {
			// Create new post
			$post_data = array(
				'post_title' => isset( $data['title'] ) ? $data['title'] : 'New Campaign',
				'post_type' => 'banner_campaign',
				'post_status' => 'draft',
				'post_author' => get_current_user_id(),
			);

			$this->id = wp_insert_post( $post_data );
			if ( is_wp_error( $this->id ) ) {
				return $this->id;
			}
		}

		// Save meta data
		$this->save_meta_data( $data );

		return true;
	}

	/**
	 * Save meta data.
	 *
	 * @param array $data Meta data to save.
	 */
	private function save_meta_data( $data ) {
		$meta_fields = array(
			'ad_type' => '_houzez_ad_type',
			'ad_zone' => '_houzez_ad_zone',
			'duration' => '_houzez_duration',
			'selected_properties' => '_houzez_selected_properties',
			'banner_image' => '_houzez_banner_image',
			'banner_link' => '_houzez_banner_link',
			'banner_alt' => '_houzez_banner_alt',
			'start_date' => '_houzez_start_date',
			'end_date' => '_houzez_end_date',
			'campaign_status' => '_houzez_campaign_status',
			'price' => '_houzez_campaign_price',
			'order_id' => '_houzez_order_id',
		);

		foreach ( $meta_fields as $field => $meta_key ) {
			if ( isset( $data[ $field ] ) ) {
				update_post_meta( $this->id, $meta_key, $data[ $field ] );
				$this->$field = $data[ $field ];
			}
		}
	}

	/**
	 * Calculate campaign price in credits.
	 *
	 * @return int Campaign price in credits.
	 */
	public function calculate_price() {
		$quantity = 1;
		if ( $this->ad_type === 'property' && is_array( $this->selected_properties ) ) {
			$quantity = count( $this->selected_properties );
		}

		return houzez_ads_calculate_campaign_price(
			$this->ad_zone,
			$this->duration,
			$quantity,
			$this->ad_type
		);
	}

	/**
	 * Process campaign payment using credits.
	 *
	 * @return bool|WP_Error True on success, WP_Error on failure.
	 */
	public function process_credit_payment() {
		$user_id = $this->user_id ? $this->user_id : get_current_user_id();
		$required_credits = $this->calculate_price();

		// Check if user has sufficient credits
		if ( ! houzez_ads_user_has_credits( $user_id, $required_credits ) ) {
			return new WP_Error( 'insufficient_credits', __( 'Insufficient credits to create this campaign.', 'houzez-ads-extension' ) );
		}

		// Deduct credits
		$deduction_result = houzez_ads_deduct_user_credits(
			$user_id,
			$required_credits,
			sprintf( __( 'Campaign: %s', 'houzez-ads-extension' ), $this->title )
		);

		if ( ! $deduction_result ) {
			return new WP_Error( 'credit_deduction_failed', __( 'Failed to deduct credits. Please try again.', 'houzez-ads-extension' ) );
		}

		// Update campaign price and status
		$this->price = $required_credits;
		$this->campaign_status = 'pending'; // Set to pending for admin approval

		update_post_meta( $this->id, '_houzez_campaign_price', $this->price );
		update_post_meta( $this->id, '_houzez_campaign_status', $this->campaign_status );

		return true;
	}

	/**
	 * Check if campaign is active.
	 *
	 * @return bool True if campaign is active.
	 */
	public function is_active() {
		return houzez_ads_is_campaign_active( $this->id );
	}

	/**
	 * Get campaign analytics.
	 *
	 * @return array Analytics data.
	 */
	public function get_analytics() {
		return houzez_ads_get_campaign_analytics( $this->id );
	}

	/**
	 * Approve campaign.
	 *
	 * @return bool True on success.
	 */
	public function approve() {
		$this->campaign_status = 'approved';
		
		// Set start date if not set
		if ( empty( $this->start_date ) ) {
			$this->start_date = current_time( 'Y-m-d H:i:s' );
		}
		
		// Calculate end date
		if ( empty( $this->end_date ) && $this->duration ) {
			$end_timestamp = strtotime( $this->start_date ) + ( $this->duration * DAY_IN_SECONDS );
			$this->end_date = date( 'Y-m-d H:i:s', $end_timestamp );
		}

		update_post_meta( $this->id, '_houzez_campaign_status', $this->campaign_status );
		update_post_meta( $this->id, '_houzez_start_date', $this->start_date );
		update_post_meta( $this->id, '_houzez_end_date', $this->end_date );

		// Update post status to published
		wp_update_post( array(
			'ID' => $this->id,
			'post_status' => 'publish'
		) );

		// Clear rotation cache
		houzez_ads_clear_rotation_cache( $this->ad_zone );

		return true;
	}

	/**
	 * Reject campaign.
	 *
	 * @param string $reason Rejection reason.
	 * @return bool True on success.
	 */
	public function reject( $reason = '' ) {
		$this->campaign_status = 'rejected';
		update_post_meta( $this->id, '_houzez_campaign_status', $this->campaign_status );
		
		if ( $reason ) {
			update_post_meta( $this->id, '_houzez_rejection_reason', $reason );
		}

		return true;
	}

	/**
	 * Get campaign display HTML.
	 *
	 * @return string Campaign HTML.
	 */
	public function get_display_html() {
		if ( ! $this->is_active() ) {
			return '';
		}

		$tracking_url = houzez_ads_generate_tracking_url( $this->id, $this->banner_link );
		
		$html = '<div class="houzez-ad-campaign" data-campaign-id="' . $this->id . '">';
		
		if ( $this->banner_image ) {
			$html .= '<a href="' . esc_url( $tracking_url ) . '" target="_blank" rel="nofollow">';
			$html .= '<img src="' . esc_url( $this->banner_image ) . '" alt="' . esc_attr( $this->banner_alt ) . '" class="houzez-ad-banner" />';
			$html .= '</a>';
		}
		
		$html .= '</div>';

		// Track impression
		houzez_ads_track_impression( $this->id );

		return $html;
	}
}
