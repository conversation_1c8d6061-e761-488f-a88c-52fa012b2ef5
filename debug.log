[01-Jul-2025 21:27:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:27:40 UTC] PHP Stack trace:
[01-Jul-2025 21:27:40 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:0
[01-Jul-2025 21:27:40 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:22
[01-Jul-2025 21:27:40 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:27:40 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:27:40 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:27:40 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:27:40 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:27:40 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:27:40 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:27:40 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:27:40 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:27:40 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:27:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:27:40 UTC] PHP Stack trace:
[01-Jul-2025 21:27:40 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:0
[01-Jul-2025 21:27:40 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:22
[01-Jul-2025 21:27:40 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:27:40 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:27:40 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:27:40 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:27:40 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:27:40 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:27:40 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:27:40 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:27:40 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:27:40 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:27:40 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:27:40 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:27:40 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:27:40 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:27:40 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:27:40 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:27:40 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:27:40 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:27:40 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:27:40 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:27:40 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:27:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:27:41 UTC] PHP Stack trace:
[01-Jul-2025 21:27:41 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:27:41 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:27:41 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:27:41 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:27:41 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:27:41 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:27:41 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:27:41 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:27:41 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:27:41 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:27:41 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:27:41 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:27:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:27:41 UTC] PHP Stack trace:
[01-Jul-2025 21:27:41 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:27:41 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:27:41 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:27:41 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:27:41 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:27:41 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:27:41 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:27:41 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:27:41 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:27:41 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:27:41 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:27:41 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:27:41 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:27:41 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:27:41 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:27:41 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:27:41 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:27:41 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:27:41 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:27:41 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:27:41 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:27:41 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:27:41 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:29:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:29:40 UTC] PHP Stack trace:
[01-Jul-2025 21:29:40 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:0
[01-Jul-2025 21:29:40 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:22
[01-Jul-2025 21:29:40 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:29:40 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:29:40 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:29:40 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:29:40 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:29:40 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:29:40 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:29:40 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:29:40 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:29:40 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:29:40 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:29:40 UTC] PHP Stack trace:
[01-Jul-2025 21:29:40 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:0
[01-Jul-2025 21:29:40 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:22
[01-Jul-2025 21:29:40 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:29:40 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:29:40 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:29:40 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:29:40 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:29:40 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:29:40 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:29:40 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:29:40 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:29:40 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:29:40 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:29:40 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:29:40 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:29:40 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:29:40 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:29:40 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:29:40 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:29:40 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:29:40 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:29:40 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:29:40 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:29:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:29:41 UTC] PHP Stack trace:
[01-Jul-2025 21:29:41 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:29:41 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:29:41 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:29:41 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:29:41 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:29:41 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:29:41 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:29:41 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:29:41 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:29:41 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:29:41 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:29:41 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:29:41 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:29:41 UTC] PHP Stack trace:
[01-Jul-2025 21:29:41 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:29:41 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:29:41 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:29:41 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:29:41 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:29:41 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:29:41 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:29:41 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:29:41 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:29:41 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:29:41 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:29:41 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:29:41 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:29:41 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:29:41 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:29:41 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:29:41 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:29:41 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:29:41 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:29:41 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:29:41 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:29:41 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:29:41 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:30:07 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:30:07 UTC] PHP Stack trace:
[01-Jul-2025 21:30:07 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/edit.php:0
[01-Jul-2025 21:30:07 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/edit.php:10
[01-Jul-2025 21:30:07 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin.php:35
[01-Jul-2025 21:30:07 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:30:07 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:30:07 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:30:07 UTC] PHP   7. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:30:07 UTC] PHP   8. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:30:07 UTC] PHP   9. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:30:07 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:30:07 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:30:07 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:30:07 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:30:07 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:30:07 UTC] PHP Stack trace:
[01-Jul-2025 21:30:07 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/edit.php:0
[01-Jul-2025 21:30:07 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/edit.php:10
[01-Jul-2025 21:30:07 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin.php:35
[01-Jul-2025 21:30:07 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:30:07 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:30:07 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:30:07 UTC] PHP   7. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:30:07 UTC] PHP   8. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:30:07 UTC] PHP   9. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:30:07 UTC] PHP  10. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:30:07 UTC] PHP  11. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:30:07 UTC] PHP  12. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:30:07 UTC] PHP  13. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:30:07 UTC] PHP  14. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:30:07 UTC] PHP  15. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:30:07 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:30:07 UTC] PHP  17. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:30:07 UTC] PHP  18. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:30:07 UTC] PHP  19. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:30:07 UTC] PHP  20. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:30:07 UTC] PHP  21. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:30:07 UTC] PHP  22. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:30:07 UTC] PHP  23. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:30:07 UTC] PHP  24. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:30:08 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:30:08 UTC] PHP Stack trace:
[01-Jul-2025 21:30:08 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:30:08 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:30:08 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:30:08 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:30:08 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:30:08 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:30:08 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:30:08 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:30:08 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:30:08 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:30:08 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:30:08 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:30:08 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:30:08 UTC] PHP Stack trace:
[01-Jul-2025 21:30:08 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:30:08 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:30:08 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:30:08 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:30:08 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:30:08 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:30:08 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:30:08 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:30:08 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:30:08 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:30:08 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:30:08 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:30:08 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:30:08 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:30:08 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:30:08 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:30:08 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:30:08 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:30:08 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:30:08 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:30:08 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:30:08 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:30:08 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:30:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:30:11 UTC] PHP Stack trace:
[01-Jul-2025 21:30:11 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:0
[01-Jul-2025 21:30:11 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:22
[01-Jul-2025 21:30:11 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:30:11 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:30:11 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:30:11 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:30:11 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:30:11 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:30:11 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:30:11 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:30:11 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:30:11 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:30:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:30:11 UTC] PHP Stack trace:
[01-Jul-2025 21:30:11 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:0
[01-Jul-2025 21:30:11 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:22
[01-Jul-2025 21:30:11 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:30:11 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:30:11 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:30:11 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:30:11 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:30:11 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:30:11 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:30:11 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:30:11 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:30:11 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:30:11 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:30:11 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:30:11 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:30:11 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:30:11 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:30:11 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:30:11 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:30:11 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:30:11 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:30:11 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:30:11 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:30:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:30:11 UTC] PHP Stack trace:
[01-Jul-2025 21:30:11 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/post.php:0
[01-Jul-2025 21:30:11 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/post.php:12
[01-Jul-2025 21:30:11 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin.php:35
[01-Jul-2025 21:30:11 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:30:11 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:30:11 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:30:11 UTC] PHP   7. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:30:11 UTC] PHP   8. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:30:11 UTC] PHP   9. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:30:11 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:30:11 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:30:11 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:30:11 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:30:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:30:11 UTC] PHP Stack trace:
[01-Jul-2025 21:30:11 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/post.php:0
[01-Jul-2025 21:30:11 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/post.php:12
[01-Jul-2025 21:30:11 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin.php:35
[01-Jul-2025 21:30:11 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:30:11 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:30:11 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:30:11 UTC] PHP   7. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:30:11 UTC] PHP   8. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:30:11 UTC] PHP   9. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:30:11 UTC] PHP  10. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:30:11 UTC] PHP  11. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:30:11 UTC] PHP  12. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:30:11 UTC] PHP  13. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:30:11 UTC] PHP  14. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:30:11 UTC] PHP  15. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:30:11 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:30:11 UTC] PHP  17. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:30:11 UTC] PHP  18. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:30:11 UTC] PHP  19. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:30:11 UTC] PHP  20. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:30:11 UTC] PHP  21. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:30:11 UTC] PHP  22. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:30:11 UTC] PHP  23. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:30:11 UTC] PHP  24. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:30:13 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:30:13 UTC] PHP Stack trace:
[01-Jul-2025 21:30:13 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:30:13 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:30:13 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:30:13 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:30:13 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:30:13 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:30:13 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:30:13 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:30:13 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:30:13 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:30:13 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:30:13 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:30:13 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:30:13 UTC] PHP Stack trace:
[01-Jul-2025 21:30:13 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:30:13 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:30:13 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:30:13 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:30:13 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:30:13 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:30:13 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:30:13 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:30:13 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:30:13 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:30:13 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:30:13 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:30:13 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:30:13 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:30:13 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:30:13 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:30:13 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:30:13 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:30:13 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:30:13 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:30:13 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:30:13 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:30:13 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:30:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:30:24 UTC] PHP Stack trace:
[01-Jul-2025 21:30:24 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:0
[01-Jul-2025 21:30:24 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:22
[01-Jul-2025 21:30:24 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:30:24 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:30:24 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:30:24 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:30:24 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:30:24 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:30:24 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:30:24 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:30:24 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:30:24 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:30:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:30:24 UTC] PHP Stack trace:
[01-Jul-2025 21:30:24 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:0
[01-Jul-2025 21:30:24 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:22
[01-Jul-2025 21:30:24 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:30:24 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:30:24 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:30:24 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:30:24 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:30:24 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:30:24 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:30:24 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:30:24 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:30:24 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:30:24 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:30:24 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:30:24 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:30:24 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:30:24 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:30:24 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:30:24 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:30:24 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:30:24 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:30:24 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:30:24 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:30:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:30:25 UTC] PHP Stack trace:
[01-Jul-2025 21:30:25 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:30:25 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:30:25 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:30:25 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:30:25 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:30:25 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:30:25 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:30:25 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:30:25 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:30:25 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:30:25 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:30:25 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:30:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:30:25 UTC] PHP Stack trace:
[01-Jul-2025 21:30:25 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:30:25 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:30:25 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:30:25 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:30:25 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:30:25 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:30:25 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:30:25 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:30:25 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:30:25 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:30:25 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:30:25 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:30:25 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:30:25 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:30:25 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:30:25 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:30:25 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:30:25 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:30:25 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:30:25 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:30:25 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:30:25 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:30:25 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:32:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:32:24 UTC] PHP Stack trace:
[01-Jul-2025 21:32:24 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:0
[01-Jul-2025 21:32:24 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:22
[01-Jul-2025 21:32:24 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:32:24 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:32:24 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:32:24 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:32:24 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:32:24 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:32:24 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:32:24 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:32:24 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:32:24 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:32:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:32:24 UTC] PHP Stack trace:
[01-Jul-2025 21:32:24 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:0
[01-Jul-2025 21:32:24 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:22
[01-Jul-2025 21:32:24 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:32:24 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:32:24 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:32:24 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:32:24 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:32:24 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:32:24 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:32:24 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:32:24 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:32:24 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:32:24 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:32:24 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:32:24 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:32:24 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:32:24 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:32:24 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:32:24 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:32:24 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:32:24 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:32:24 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:32:24 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:32:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:32:25 UTC] PHP Stack trace:
[01-Jul-2025 21:32:25 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:32:25 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:32:25 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:32:25 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:32:25 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:32:25 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:32:25 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:32:25 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:32:25 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:32:25 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:32:25 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:32:25 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:32:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:32:25 UTC] PHP Stack trace:
[01-Jul-2025 21:32:25 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:32:25 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:32:25 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:32:25 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:32:25 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:32:25 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:32:25 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:32:25 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:32:25 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:32:25 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:32:25 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:32:25 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:32:25 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:32:25 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:32:25 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:32:25 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:32:25 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:32:25 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:32:25 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:32:25 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:32:25 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:32:25 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:32:25 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:34:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:34:24 UTC] PHP Stack trace:
[01-Jul-2025 21:34:24 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:0
[01-Jul-2025 21:34:24 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:22
[01-Jul-2025 21:34:24 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:34:24 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:34:24 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:34:24 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:34:24 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:34:24 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:34:24 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:34:24 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:34:24 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:34:24 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:34:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:34:24 UTC] PHP Stack trace:
[01-Jul-2025 21:34:24 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:0
[01-Jul-2025 21:34:24 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:22
[01-Jul-2025 21:34:24 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:34:24 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:34:24 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:34:24 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:34:24 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:34:24 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:34:24 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:34:24 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:34:24 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:34:24 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:34:24 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:34:24 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:34:24 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:34:24 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:34:24 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:34:24 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:34:24 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:34:24 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:34:24 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:34:24 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:34:24 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:34:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:34:25 UTC] PHP Stack trace:
[01-Jul-2025 21:34:25 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:34:25 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:34:25 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:34:25 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:34:25 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:34:25 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:34:25 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:34:25 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:34:25 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:34:25 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:34:25 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:34:25 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:34:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:34:25 UTC] PHP Stack trace:
[01-Jul-2025 21:34:25 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:34:25 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:34:25 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:34:25 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:34:25 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:34:25 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:34:25 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:34:25 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:34:25 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:34:25 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:34:25 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:34:25 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:34:25 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:34:25 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:34:25 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:34:25 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:34:25 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:34:25 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:34:25 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:34:25 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:34:25 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:34:25 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:34:25 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:36:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:36:24 UTC] PHP Stack trace:
[01-Jul-2025 21:36:24 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:0
[01-Jul-2025 21:36:24 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:22
[01-Jul-2025 21:36:24 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:36:24 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:36:24 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:36:24 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:36:24 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:36:24 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:36:24 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:36:24 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:36:24 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:36:24 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:36:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:36:24 UTC] PHP Stack trace:
[01-Jul-2025 21:36:24 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:0
[01-Jul-2025 21:36:24 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:22
[01-Jul-2025 21:36:24 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:36:24 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:36:24 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:36:24 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:36:24 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:36:24 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:36:24 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:36:24 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:36:24 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:36:24 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:36:24 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:36:24 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:36:24 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:36:24 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:36:24 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:36:24 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:36:24 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:36:24 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:36:24 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:36:24 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:36:24 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:36:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:36:25 UTC] PHP Stack trace:
[01-Jul-2025 21:36:25 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:36:25 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:36:25 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:36:25 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:36:25 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:36:25 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:36:25 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:36:25 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:36:25 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:36:25 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:36:25 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:36:25 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:36:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:36:25 UTC] PHP Stack trace:
[01-Jul-2025 21:36:25 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:36:25 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:36:25 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:36:25 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:36:25 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:36:25 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:36:25 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:36:25 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:36:25 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:36:25 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:36:25 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:36:25 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:36:25 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:36:25 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:36:25 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:36:25 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:36:25 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:36:25 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:36:25 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:36:25 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:36:25 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:36:25 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:36:25 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:38:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:38:24 UTC] PHP Stack trace:
[01-Jul-2025 21:38:24 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:0
[01-Jul-2025 21:38:24 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:22
[01-Jul-2025 21:38:24 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:38:24 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:38:24 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:38:24 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:38:24 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:38:24 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:38:24 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:38:24 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:38:24 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:38:24 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:38:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:38:24 UTC] PHP Stack trace:
[01-Jul-2025 21:38:24 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:0
[01-Jul-2025 21:38:24 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:22
[01-Jul-2025 21:38:24 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:38:24 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:38:24 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:38:24 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:38:24 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:38:24 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:38:24 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:38:24 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:38:24 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:38:24 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:38:24 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:38:24 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:38:24 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:38:24 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:38:24 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:38:24 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:38:24 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:38:24 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:38:24 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:38:24 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:38:24 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:38:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:38:25 UTC] PHP Stack trace:
[01-Jul-2025 21:38:25 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:38:25 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:38:25 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:38:25 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:38:25 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:38:25 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:38:25 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:38:25 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:38:25 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:38:25 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:38:25 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:38:25 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:38:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:38:25 UTC] PHP Stack trace:
[01-Jul-2025 21:38:25 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:38:25 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:38:25 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:38:25 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:38:25 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:38:25 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:38:25 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:38:25 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:38:25 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:38:25 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:38:25 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:38:25 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:38:25 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:38:25 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:38:25 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:38:25 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:38:25 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:38:25 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:38:25 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:38:25 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:38:25 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:38:25 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:38:25 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:40:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:40:24 UTC] PHP Stack trace:
[01-Jul-2025 21:40:24 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:0
[01-Jul-2025 21:40:24 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:22
[01-Jul-2025 21:40:24 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:40:24 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:40:24 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:40:24 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:40:24 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:40:24 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:40:24 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:40:24 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:40:24 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:40:24 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:40:24 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:40:24 UTC] PHP Stack trace:
[01-Jul-2025 21:40:24 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:0
[01-Jul-2025 21:40:24 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin-ajax.php:22
[01-Jul-2025 21:40:24 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:40:24 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:40:24 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:40:24 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:40:24 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:40:24 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:40:24 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:40:24 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:40:24 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:40:24 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:40:24 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:40:24 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:40:24 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:40:24 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:40:24 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:40:24 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:40:24 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:40:24 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:40:24 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:40:24 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:40:24 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:40:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:40:25 UTC] PHP Stack trace:
[01-Jul-2025 21:40:25 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:40:25 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:40:25 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:40:25 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:40:25 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:40:25 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[01-Jul-2025 21:40:25 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[01-Jul-2025 21:40:25 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[01-Jul-2025 21:40:25 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:40:25 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:40:25 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:40:25 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[01-Jul-2025 21:40:25 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[01-Jul-2025 21:40:25 UTC] PHP Stack trace:
[01-Jul-2025 21:40:25 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[01-Jul-2025 21:40:25 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[01-Jul-2025 21:40:25 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[01-Jul-2025 21:40:25 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[01-Jul-2025 21:40:25 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[01-Jul-2025 21:40:25 UTC] PHP   6. Realtyna\Core\StartUp->__construct($config = class Realtyna\Core\Config { protected array $config = ['name' => 'Realtyna Mls On The Fly', 'slug' => 'realtyna-mls-on-the-fly', 'text-domain' => 'realtyna-mls-on-the-fly', 'log' => [...]] }) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/mls-on-the-fly.php:68
[01-Jul-2025 21:40:25 UTC] PHP   7. Realtyna\MlsOnTheFly\Main->components() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:68
[01-Jul-2025 21:40:25 UTC] PHP   8. Realtyna\Core\StartUp->addComponent($component = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Main.php:31
[01-Jul-2025 21:40:25 UTC] PHP   9. Realtyna\Core\Utilities\Container->get($abstract = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/StartUp.php:82
[01-Jul-2025 21:40:25 UTC] PHP  10. Realtyna\Core\Utilities\Container->resolve($concrete = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\CloudPostComponent') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:77
[01-Jul-2025 21:40:25 UTC] PHP  11. ReflectionClass->newInstanceArgs($args = []) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:40:25 UTC] PHP  12. Realtyna\Core\Abstracts\ComponentAbstract->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Utilities/Container.php:118
[01-Jul-2025 21:40:25 UTC] PHP  13. Realtyna\MlsOnTheFly\Components\CloudPost\CloudPostComponent->subComponents() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:26
[01-Jul-2025 21:40:25 UTC] PHP  14. Realtyna\Core\Abstracts\ComponentAbstract->addSubComponent($subComponentClass = 'Realtyna\\MlsOnTheFly\\Components\\CloudPost\\SubComponents\\RFClient\\RFClientComponent', $container = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/CloudPostComponent.php:46
[01-Jul-2025 21:40:25 UTC] PHP  15. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->register() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/vendor/realtyna/wp-plugin-framework/src/Abstracts/ComponentAbstract.php:160
[01-Jul-2025 21:40:25 UTC] PHP  16. Realtyna\MlsOnTheFly\Components\CloudPost\SubComponents\RFClient\RFClientComponent->initRFClient() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:25
[01-Jul-2025 21:40:25 UTC] PHP  17. __($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/mls-on-the-fly/src/Components/CloudPost/SubComponents/RFClient/RFClientComponent.php:59
[01-Jul-2025 21:40:25 UTC] PHP  18. translate($text = 'RF credentials are missing in settings.', $domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:307
[01-Jul-2025 21:40:25 UTC] PHP  19. get_translations_for_domain($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:195
[01-Jul-2025 21:40:25 UTC] PHP  20. _load_textdomain_just_in_time($domain = 'mls-on-the-fly') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[01-Jul-2025 21:40:25 UTC] PHP  21. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[01-Jul-2025 21:40:25 UTC] PHP  22. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[01-Jul-2025 21:40:25 UTC] PHP  23. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>mls-on-the-fly</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
